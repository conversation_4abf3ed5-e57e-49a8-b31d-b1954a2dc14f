# 🏗️ 系统架构模式 - 关键技术决策

## 🎯 核心架构模式 [RS:5]

**架构名称**: 零服务器动态模块化架构 (Zero-Server Dynamic Modular Architecture)

### 设计原则
1. **Source of Truth**: 使用根ESM模块直到src/迁移落地
2. **UI Contract**: 保持所有必需的DOM IDs不变
3. **Runtime Entry**: 单页面应用，CDN库加载
4. **Concurrency**: 尊重MAX_CONCURRENCY=50，API工作在TaskPool内
5. **Libraries**: ECharts 5.x (global echarts), PapaParse 5.x (global Papa)

## 📁 模块化设计模式 [RS:5]

### 文件组织结构
```
refactored/solution1-es6-modules/
├── index.html              # 🏠 运行时入口 (1KB)
├── loader.js               # 🔄 模块加载器 (3KB)
├── src/
│   ├── constants.js        # ⚙️ 全局配置 (1KB)
│   ├── storage.js          # 💾 数据层 (8KB)
│   ├── parser.js           # 📝 解析层 (12KB)
│   ├── charts.js           # 📊 图表层 (6KB)
│   ├── drag-upload.js      # 📁 上传层 (4KB)
│   └── main.js             # 🎯 应用层 (8KB)
├── config/
│   ├── local-config.example.js  # 🔑 配置模板
│   └── local-config.js     # 🔐 用户密钥 (gitignore)
```

### 模块依赖关系 [RS:4]
```
constants.js (基础层)
    ↓
storage.js, parser.js, charts.js, drag-upload.js (功能层)
    ↓
main.js (应用层)
    ↓
loader.js (加载层)
    ↓
index.html (入口层)
```

## 🔄 动态加载模式 [RS:5]

### 加载策略
**问题**: file://协议下ES6 import语句会触发CORS错误
**解决方案**: 动态脚本注入 + 模块转换

### 实现模式
```javascript
// loader.js核心模式
const MODULE_GRAPH = {
    'constants.js': [],
    'storage.js': ['constants.js'],
    'parser.js': ['constants.js'],
    'charts.js': ['constants.js'],
    'drag-upload.js': [],
    'main.js': ['constants.js', 'storage.js', 'parser.js', 'charts.js', 'drag-upload.js']
};

async function loadModule(modulePath) {
    const response = await fetch(modulePath);
    const code = await response.text();
    const transformedCode = transformESMSyntax(code);
    const script = document.createElement('script');
    script.textContent = transformedCode;
    document.head.appendChild(script);
}
```

## 🎯 并发控制模式 [RS:4]

### TaskPool设计模式
```javascript
class TaskPool {
    constructor(maxConcurrency = 50) {
        this.maxConcurrency = maxConcurrency;
        this.running = new Set();
        this.queue = [];
    }
    
    async execute(task) {
        return new Promise((resolve, reject) => {
            this.queue.push({ task, resolve, reject });
            this.processQueue();
        });
    }
    
    processQueue() {
        while (this.running.size < this.maxConcurrency && this.queue.length > 0) {
            const { task, resolve, reject } = this.queue.shift();
            this.runTask(task, resolve, reject);
        }
    }
}
```

## 📊 数据流模式 [RS:4]

### 单向数据流
```
文件输入 → 解析处理 → AI分析 → 数据存储 → 图表更新 → 结果导出
```

### 数据契约
```javascript
interface ConversationData {
    fileName: string;
    timestamp: number;
    messages: Array<{
        time: string;
        speaker: string;
        content: string;
        type: 'driver' | 'support';
    }>;
    analysis?: {
        effectiveness: number;     // 1-5
        satisfaction: number;      // 1-5
        knowledge_covered: number; // 1-5
        question_category: string;
    };
}
```

## 🔧 配置管理模式 [RS:3]

### 分层配置策略
1. **constants.js**: 系统级常量，版本控制
2. **local-config.js**: 用户级配置，不入版本控制
3. **运行时配置**: 动态调整参数

### 配置优先级
```
运行时配置 > local-config.js > constants.js > 默认值
```

## 🎨 UI契约模式 [RS:4]

### DOM ID契约
保持与standalone.html完全一致的DOM结构：
```html
<!-- 必需的DOM容器 -->
<input type="file" id="file-input" multiple accept=".txt">
<button id="start-btn">开始分析</button>
<button id="export-btn">导出数据</button>
<div id="chart-questions"></div>
<div id="chart-effectiveness"></div>
<div id="chart-satisfaction"></div>
<div id="chart-knowledge"></div>
```

## 🔄 错误处理模式 [RS:3]

### 分层错误处理
1. **模块级**: 各模块内部错误捕获和恢复
2. **应用级**: main.js统一错误处理
3. **系统级**: loader.js加载失败回退

### 错误恢复策略
```javascript
// 优雅降级模式
try {
    await loadModularVersion();
} catch (error) {
    console.warn('模块化版本加载失败，回退到standalone版本');
    window.location.href = 'standalone.html';
}
```

## 📈 性能优化模式 [RS:3]

### 懒加载策略
- 按需加载非核心模块
- 图表库延迟初始化
- 大数据集分批处理

### 内存管理
```javascript
// 内存清理模式
window.addEventListener('beforeunload', () => {
    // 清理大对象
    window.parseResults = null;
    window.chartInstances = null;
});
```

## 🔐 安全模式 [RS:2]

### 数据安全
- 本地数据处理，不上传云端
- API密钥本地存储
- 敏感信息不入版本控制

### 代码安全
- 避免eval()和Function()构造器
- 输入验证和清理
- XSS防护

## 🧪 测试模式 [RS:2]

### 测试策略
1. **单元测试**: 各模块独立测试
2. **集成测试**: 模块间接口测试
3. **端到端测试**: 完整功能流程测试
4. **兼容性测试**: 多浏览器环境测试

### 测试数据
- 使用New folder/中的真实数据
- 创建边界条件测试用例
- 模拟错误场景

## 📚 文档模式 [RS:2]

### 文档分层
1. **用户文档**: 使用指南和FAQ
2. **开发文档**: API接口和架构说明
3. **部署文档**: 安装和配置指南
4. **维护文档**: 故障排除和更新指南

### 文档更新策略
- 代码变更同步更新文档
- 版本发布前文档审查
- 用户反馈驱动文档改进
