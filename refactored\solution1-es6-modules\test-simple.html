<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>简单模块测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        #output { background: #000; color: #0f0; padding: 10px; height: 400px; overflow-y: auto; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🔧 模块修复测试</h1>
    <div id="results"></div>
    <h2>控制台输出</h2>
    <div id="output"></div>

    <script>
        // 捕获控制台输出
        const output = document.getElementById('output');
        const results = document.getElementById('results');
        
        function log(message, type = 'info') {
            output.textContent += `[${type.toUpperCase()}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }
        
        function showResult(name, success, message) {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${name}:</strong> ${message}`;
            results.appendChild(div);
        }
        
        // 初始化全局导出对象
        window.ModuleExports = {};
        
        // 测试模块加载
        async function testModule(name, path) {
            return new Promise((resolve) => {
                log(`测试模块: ${name}`);
                
                const script = document.createElement('script');
                script.src = path;
                script.onload = () => {
                    const exports = window.ModuleExports[name];
                    if (exports) {
                        const keys = Object.keys(exports);
                        log(`✅ ${name} 成功 - 导出: ${keys.join(', ')}`);
                        showResult(name, true, `成功加载，导出 ${keys.length} 个对象`);
                    } else {
                        log(`⚠️ ${name} 加载但无导出`);
                        showResult(name, false, '加载成功但无全局导出');
                    }
                    resolve(true);
                };
                script.onerror = () => {
                    log(`❌ ${name} 加载失败`);
                    showResult(name, false, '脚本加载失败');
                    resolve(false);
                };
                document.head.appendChild(script);
            });
        }
        
        // 开始测试
        async function runTests() {
            log('🧪 开始模块修复测试...');
            
            // 按依赖顺序测试
            await testModule('constants.js', './src/constants.js');
            await new Promise(r => setTimeout(r, 100));
            
            await testModule('utils.js', './src/utils.js');
            await new Promise(r => setTimeout(r, 100));
            
            await testModule('tag-center.js', './src/tag-center.js');
            await new Promise(r => setTimeout(r, 100));
            
            await testModule('qa-optimization.js', './src/qa-optimization.js');
            await new Promise(r => setTimeout(r, 100));
            
            await testModule('module-loader-optimizer.js', './src/module-loader-optimizer.js');
            
            log('🎯 测试完成');
            const totalModules = Object.keys(window.ModuleExports).length;
            showResult('总结', totalModules > 0, `成功加载 ${totalModules} 个模块`);
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
