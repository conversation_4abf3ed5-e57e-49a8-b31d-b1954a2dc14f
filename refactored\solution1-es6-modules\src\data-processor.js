/* === Naming Conventions Header (auto-injected 2025-08-14) ===
    Scoped constant object: DATA_STORAGE_KEYS (legacy alias STORAGE_KEYS exported only for compat)
    Pattern rule: never declare plain STORAGE_KEYS outside constants.js
*/
/**
 * ==================== GoMyHire 对话分析系统 - 数据处理器 ====================
 * 
 * @MODULE_INFO
 * 模块名称: Data Processor System
 * 版本: 2.0.0
 * 功能描述: 从standalone.html完整迁移的数据处理系统，实现复杂的数据处理和导出逻辑
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: utils.js (formatDate, safeExecute, generateUniqueId), storage-manager.js (StorageManager)
 * 间接依赖: 无
 * 被依赖: main.js, report-generator.js, qa-optimization.js
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 2 (第三层，数据处理模块)
 * 加载时机: after-core (核心模块加载后)
 * 加载条件: 依赖utils.js和storage-manager.js加载完成
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - 数据处理器类 (DataProcessor类)
 *   - CSV导出功能
 *   - 问答数据集管理
 *   - 统计分析功能
 *   - 报告生成支持
 * 导出接口: DataProcessor, getDataProcessor
 * 全局注册: window.ModuleExports['data-processor.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 原始对话数据、筛选条件、导出配置
 * 输出数据: 处理后的数据、CSV文件、统计报告
 * 状态管理: 维护数据缓存、处理状态
 * 事件处理: 数据处理完成事件
 * 
 * @INTEGRATION (集成关系)
 * 存储集成: 与storage-manager深度集成
 * UI集成: 为UI提供数据处理服务
 * 报告集成: 为报告生成提供数据处理能力
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 中等 (缓存处理数据)
 * 加载性能: 中等 (依赖其他模块)
 * 运行时性能: 高效 (优化的数据处理算法)
 */

// 获取工具函数和依赖模块
function getUtils() {
    return window.ModuleExports && window.ModuleExports['utils.js'];
}

function getStorageManager() {
    const storageModule = window.ModuleExports && window.ModuleExports['storage-manager.js'];
    return storageModule ? storageModule.getStorageManager() : null;
}

// 存储键常量 (重命名以避免与 constants.js 中的 STORAGE_KEYS 发生顶层重复声明冲突)
const DATA_STORAGE_KEYS = {
    DRIVERS: 'drivers',
    SUPPORT_AGENTS: 'supportAgents',
    QUESTION_TAGS: 'questionTags',
    DETAILED_QUESTIONS: 'detailedQuestions',
    QA_DATASET: 'qaDataset',
    QUESTION_CATEGORIES: 'questionCategories',
    KNOWLEDGE: 'knowledge'
};

// ==================== 数据处理器类 ====================
/**
 * 数据处理器类 - 负责数据的导出、统计和报告生成
 * @SERVICE 数据处理和导出管理器
 */
// ==================== 数据处理器类 ====================
/**
 * 数据处理器类 - 负责数据的导出、统计和报告生成
 * @SERVICE 数据处理和导出管理器
 */
class DataProcessor {
    constructor(storageManager = null, eventBus = null) {
        this.eventBus = eventBus;
        const storageModule = window.ModuleExports && window.ModuleExports['storage-manager.js'];
        this.storage = storageManager || 
                      (storageModule ? new storageModule.StorageManager() : getStorageManager()) ||
                      null;
        this.data = this.storage ? this.loadData() : {};
        
        console.log('📊 DataProcessor 初始化完成');
    }

    /**
     * 加载所有数据
     * @SERVICE 数据加载方法
     * @returns {Object} 加载的数据对象
     */
    loadData() {
        return {
            drivers: this.storage.load(DATA_STORAGE_KEYS.DRIVERS, {}),
            supportAgents: this.storage.load(DATA_STORAGE_KEYS.SUPPORT_AGENTS, {}),
            questionTags: this.storage.load(DATA_STORAGE_KEYS.QUESTION_TAGS, {}),
            detailedQuestions: this.storage.load(DATA_STORAGE_KEYS.DETAILED_QUESTIONS, []),
            qaDataset: this.storage.load(DATA_STORAGE_KEYS.QA_DATASET, []),
            questionCategories: this.storage.load(DATA_STORAGE_KEYS.QUESTION_CATEGORIES, {}),
            knowledge: this.storage.load(DATA_STORAGE_KEYS.KNOWLEDGE, []),
            metrics: [],
        };
    }

    /**
     * 保存数据
     * @SERVICE 数据保存方法
     * @param {string} key - 数据键
     * @param {any} data - 数据
     */
    saveData(key, data) {
        this.data[key] = data;
    this.storage.save(DATA_STORAGE_KEYS[key.toUpperCase()], data);
    }

    /**
     * 获取问答数据集
     * @SERVICE 问答数据集获取方法
     * @param {Object} filters - 筛选条件
     * @returns {Array} 筛选后的问答数据集
     */
    getQADataset(filters = {}) {
        let dataset = [...this.data.qaDataset];

        // 应用筛选条件
        if (filters.keyword) {
            const keyword = filters.keyword.toLowerCase();
            dataset = dataset.filter(qa =>
                (qa.question && qa.question.toLowerCase().includes(keyword)) ||
                (qa.answer && qa.answer.toLowerCase().includes(keyword))
            );
        }

        if (filters.difficulty) {
            dataset = dataset.filter(qa => qa.difficulty === filters.difficulty);
        }

        if (filters.isCommon !== undefined) {
            dataset = dataset.filter(qa => qa.isCommon === filters.isCommon);
        }

        if (filters.tags && filters.tags.length > 0) {
            dataset = dataset.filter(qa =>
                qa.tags && qa.tags.some(tag => filters.tags.includes(tag))
            );
        }

        return dataset;
    }

    /**
     * 导出问答题集为CSV
     * @SERVICE CSV导出方法
     * @param {Object} filters - 筛选条件
     * @returns {string|null} CSV内容或null
     */
    exportQADatasetCSV(filters = {}) {
        const dataset = this.getQADataset(filters);

        if (dataset.length === 0) {
            return null;
        }

        const headers = [
            'ID', '问题', '答案', '操作步骤', '适用场景',
            '难度等级', '是否通用', '标签', '频次',
            '创建时间', '更新时间', '数据源'
        ];

        const rows = dataset.map(qa => [
            qa.id || '',
            qa.question || '',
            qa.answer || '',
            qa.steps || '',
            qa.scenario || '',
            qa.difficulty || '',
            qa.isCommon ? '是' : '否',
            (qa.tags || []).join(';'),
            qa.frequency || 0,
            qa.createdAt ? (getUtils()?.formatDate(qa.createdAt) || qa.createdAt) : '',
            qa.updatedAt ? (getUtils()?.formatDate(qa.updatedAt) || qa.updatedAt) : '',
            qa.source || ''
        ]);

        // 构建CSV内容
        const csvContent = [headers, ...rows]
            .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
            .join('\n');

        return csvContent;
    }

    /**
     * 导出问答题集为JSON
     * @SERVICE JSON导出方法
     * @param {Object} filters - 筛选条件
     * @returns {string|null} JSON内容或null
     */
    exportQADatasetJSON(filters = {}) {
        const dataset = this.getQADataset(filters);

        if (dataset.length === 0) {
            return null;
        }

        const exportData = {
            exportInfo: {
                timestamp: new Date().toISOString(),
                totalCount: dataset.length,
                filters: filters,
                version: '1.0'
            },
            data: dataset
        };

        return JSON.stringify(exportData, null, 2);
    }

    /**
     * 计算统计数据
     * @SERVICE 统计计算方法
     * @returns {Object} 统计数据
     */
    calculateStatistics() {
        const stats = {
            totalDrivers: Object.keys(this.data.drivers).length,
            totalSupportAgents: Object.keys(this.data.supportAgents).length,
            totalQuestions: this.data.detailedQuestions.length,
            totalQAItems: this.data.qaDataset.length,
            totalKnowledge: this.data.knowledge.length,
            avgSatisfaction: 0,
            avgResolutionRate: 0,
            avgResponseTime: 0,
            questionsByCategory: { ...this.data.questionCategories },
            questionsByTag: { ...this.data.questionTags }
        };

        // 计算平均值
        if (this.data.detailedQuestions.length > 0) {
            const satisfactionSum = this.data.detailedQuestions.reduce((sum, q) => sum + (q.satisfaction || 0), 0);
            const resolutionSum = this.data.detailedQuestions.reduce((sum, q) => sum + (q.resolutionRate || 0), 0);
            const responseTimeSum = this.data.detailedQuestions.reduce((sum, q) => sum + (q.responseTime || 0), 0);

            stats.avgSatisfaction = satisfactionSum / this.data.detailedQuestions.length;
            stats.avgResolutionRate = resolutionSum / this.data.detailedQuestions.length;
            stats.avgResponseTime = responseTimeSum / this.data.detailedQuestions.length;
        }

        return stats;
    }

    /**
     * 添加问答项目
     * @SERVICE 问答项目添加方法
     * @param {Object} qaData - 问答数据
     * @returns {boolean} 是否添加成功
     */
    addQAItem(qaData) {
        try {
            const qaItem = {
                id: qaData.id || generateUniqueId('qa'),
                question: qaData.question || '',
                answer: qaData.answer || '',
                steps: qaData.steps || '',
                scenario: qaData.scenario || '',
                difficulty: qaData.difficulty || 'medium',
                isCommon: qaData.isCommon || false,
                tags: qaData.tags || [],
                frequency: qaData.frequency || 1,
                source: qaData.source || 'manual',
                createdAt: qaData.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // 检查是否已存在相似问题
            const existingIndex = this.data.qaDataset.findIndex(item =>
                item.question === qaItem.question || item.id === qaItem.id
            );

            if (existingIndex !== -1) {
                // 更新现有项目
                this.data.qaDataset[existingIndex] = { ...this.data.qaDataset[existingIndex], ...qaItem };
                console.log(`✓ 问答项目已更新: ${qaItem.id}`);
            } else {
                // 添加新项目
                this.data.qaDataset.push(qaItem);
                console.log(`✓ 问答项目已添加: ${qaItem.id}`);
            }

            // 保存到存储
            this.saveData('qaDataset', this.data.qaDataset);
            return true;
        } catch (error) {
            console.error('添加问答项目失败:', error);
            return false;
        }
    }

    /**
     * 批量添加问答项目
     * @SERVICE 批量问答项目添加方法
     * @param {Array} qaArray - 问答数据数组
     * @returns {Object} 添加结果统计
     */
    addQAItems(qaArray) {
        let addedCount = 0;
        let updatedCount = 0;

        qaArray.forEach(qaData => {
            const originalLength = this.data.qaDataset.length;
            if (this.addQAItem(qaData)) {
                if (this.data.qaDataset.length > originalLength) {
                    addedCount++;
                } else {
                    updatedCount++;
                }
            }
        });

        console.log(`📦 批量添加完成: 新增 ${addedCount}, 更新 ${updatedCount}`);
        return { addedCount, updatedCount };
    }

    /**
     * 删除问答项目
     * @SERVICE 问答项目删除方法
     * @param {string} qaId - 问答项目ID
     * @returns {boolean} 是否删除成功
     */
    removeQAItem(qaId) {
        const index = this.data.qaDataset.findIndex(item => item.id === qaId);
        if (index !== -1) {
            this.data.qaDataset.splice(index, 1);
            this.saveData('qaDataset', this.data.qaDataset);
            console.log(`🗑️ 问答项目已删除: ${qaId}`);
            return true;
        }
        return false;
    }

    /**
     * 搜索问答项目
     * @SERVICE 问答项目搜索方法
     * @param {string} keyword - 搜索关键词
     * @param {Object} options - 搜索选项
     * @returns {Array} 搜索结果
     */
    searchQAItems(keyword, options = {}) {
        const { limit = 50, sortBy = 'relevance' } = options;
        const lowerKeyword = keyword.toLowerCase();

        let results = this.data.qaDataset.filter(qa => {
            const questionMatch = qa.question && qa.question.toLowerCase().includes(lowerKeyword);
            const answerMatch = qa.answer && qa.answer.toLowerCase().includes(lowerKeyword);
            const tagsMatch = qa.tags && qa.tags.some(tag => tag.toLowerCase().includes(lowerKeyword));
            
            return questionMatch || answerMatch || tagsMatch;
        });

        // 排序
        if (sortBy === 'relevance') {
            results.sort((a, b) => {
                const aScore = this.calculateRelevanceScore(a, lowerKeyword);
                const bScore = this.calculateRelevanceScore(b, lowerKeyword);
                return bScore - aScore;
            });
        } else if (sortBy === 'frequency') {
            results.sort((a, b) => (b.frequency || 0) - (a.frequency || 0));
        }

        return results.slice(0, limit);
    }

    /**
     * 计算相关性分数
     * @UTIL 相关性分数计算工具
     * @param {Object} qa - 问答项目
     * @param {string} keyword - 关键词
     * @returns {number} 相关性分数
     */
    calculateRelevanceScore(qa, keyword) {
        let score = 0;
        
        if (qa.question && qa.question.toLowerCase().includes(keyword)) {
            score += 10;
        }
        if (qa.answer && qa.answer.toLowerCase().includes(keyword)) {
            score += 5;
        }
        if (qa.tags && qa.tags.some(tag => tag.toLowerCase().includes(keyword))) {
            score += 3;
        }
        
        // 频次加权
        score += (qa.frequency || 0) * 0.1;
        
        return score;
    }

    /**
     * 导出所有数据
     * @SERVICE 全量数据导出方法
     * @returns {Object} 导出数据
     */
    exportAllData() {
        return {
            data: { ...this.data },
            timestamp: Date.now(),
            version: '1.0',
            statistics: this.calculateStatistics()
        };
    }

    /**
     * 导入数据
     * @SERVICE 数据导入方法
     * @param {Object} importedData - 导入的数据
     * @returns {boolean} 是否导入成功
     */
    importData(importedData) {
        try {
            if (!importedData || !importedData.data) {
                throw new Error('无效的导入数据格式');
            }

            // 合并数据
            Object.keys(importedData.data).forEach(key => {
                if (this.data.hasOwnProperty(key)) {
                    this.data[key] = importedData.data[key];
                    this.saveData(key, this.data[key]);
                }
            });

            console.log('✅ 数据导入成功');
            return true;
        } catch (error) {
            console.error('数据导入失败:', error);
            return false;
        }
    }

    /**
     * 清理数据
     * @SERVICE 数据清理方法
     * @param {Object} options - 清理选项
     * @returns {Object} 清理结果统计
     */
    cleanupData(options = {}) {
        const { removeEmpty = true, removeDuplicates = true, maxAge = null } = options;
        const results = { removed: 0, cleaned: 0 };

        // 清理问答数据集
        if (removeEmpty || removeDuplicates || maxAge) {
            const originalLength = this.data.qaDataset.length;
            
            this.data.qaDataset = this.data.qaDataset.filter(qa => {
                // 移除空项目
                if (removeEmpty && (!qa.question || !qa.answer)) {
                    return false;
                }
                
                // 移除过期项目
                if (maxAge && qa.createdAt) {
                    const age = Date.now() - new Date(qa.createdAt).getTime();
                    if (age > maxAge) {
                        return false;
                    }
                }
                
                return true;
            });

            // 移除重复项目
            if (removeDuplicates) {
                const seen = new Set();
                this.data.qaDataset = this.data.qaDataset.filter(qa => {
                    const key = `${qa.question}_${qa.answer}`;
                    if (seen.has(key)) {
                        return false;
                    }
                    seen.add(key);
                    return true;
                });
            }

            results.removed = originalLength - this.data.qaDataset.length;
            this.saveData('qaDataset', this.data.qaDataset);
        }

        console.log(`🧹 数据清理完成: 移除 ${results.removed} 项`);
        return results;
    }
}


// ==================== 全局数据处理器实例 ====================
let dataProcessorInstance = null;

/**
 * 获取全局数据处理器实例
 * @SERVICE 全局数据处理器获取函数
 * @param {Object} storageManager - 存储管理器实例
 * @param {Object} eventBus - 事件总线实例
 * @returns {DataProcessor} 数据处理器实例
 */
function getDataProcessor(storageManager = null, eventBus = null) {
    if (!dataProcessorInstance) {
        dataProcessorInstance = new DataProcessor(storageManager, eventBus);
    }
    return dataProcessorInstance;
}

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['data-processor.js'] = {
    DataProcessor,
    getDataProcessor,
    // 向后兼容: 仍然导出 STORAGE_KEYS 名称，但内部实际使用 DATA_STORAGE_KEYS
    STORAGE_KEYS: DATA_STORAGE_KEYS,
    DATA_STORAGE_KEYS
};
