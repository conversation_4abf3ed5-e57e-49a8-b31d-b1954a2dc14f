<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部署验证 - GoMyHire 对话分析系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .test-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .test-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
        }
        
        .test-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-running {
            background: #cce5ff;
            color: #0066cc;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .test-body {
            padding: 20px;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-icon {
            width: 24px;
            height: 24px;
            margin-right: 15px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .icon-pending {
            background: #ffc107;
            color: white;
        }
        
        .icon-success {
            background: #28a745;
            color: white;
        }
        
        .icon-error {
            background: #dc3545;
            color: white;
        }
        
        .test-description {
            flex: 1;
            font-size: 1em;
            color: #555;
        }
        
        .test-result {
            font-size: 0.9em;
            color: #777;
            margin-left: 10px;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: transform 0.2s;
            border: none;
            cursor: pointer;
            font-size: 1em;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        
        .summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
            text-align: center;
        }
        
        .summary h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .summary-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        
        .log-output {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 15px;
            border-radius: 6px;
            max-height: 200px;
            overflow-y: auto;
            font-size: 0.9em;
            line-height: 1.4;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 部署验证</h1>
            <p>GoMyHire 对话分析系统 - 模块化版本</p>
        </div>
        
        <div class="content">
            <!-- 环境检测 -->
            <div class="test-section">
                <div class="test-header">
                    <div class="test-title">🌐 环境检测</div>
                    <div class="test-status status-pending" id="env-status">待检测</div>
                </div>
                <div class="test-body">
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="browser-icon">?</div>
                        <div class="test-description">浏览器兼容性检查</div>
                        <div class="test-result" id="browser-result">检测中...</div>
                    </div>
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="protocol-icon">?</div>
                        <div class="test-description">file:// 协议支持</div>
                        <div class="test-result" id="protocol-result">检测中...</div>
                    </div>
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="storage-icon">?</div>
                        <div class="test-description">localStorage 可用性</div>
                        <div class="test-result" id="storage-result">检测中...</div>
                    </div>
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="es6-icon">?</div>
                        <div class="test-description">ES6 模块支持</div>
                        <div class="test-result" id="es6-result">检测中...</div>
                    </div>
                </div>
            </div>
            
            <!-- 依赖检测 -->
            <div class="test-section">
                <div class="test-header">
                    <div class="test-title">📦 依赖检测</div>
                    <div class="test-status status-pending" id="deps-status">待检测</div>
                </div>
                <div class="test-body">
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="echarts-icon">?</div>
                        <div class="test-description">ECharts 图表库</div>
                        <div class="test-result" id="echarts-result">检测中...</div>
                    </div>
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="papa-icon">?</div>
                        <div class="test-description">Papa Parse CSV库</div>
                        <div class="test-result" id="papa-result">检测中...</div>
                    </div>
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="fontawesome-icon">?</div>
                        <div class="test-description">Font Awesome 图标</div>
                        <div class="test-result" id="fontawesome-result">检测中...</div>
                    </div>
                </div>
            </div>
            
            <!-- 文件检测 -->
            <div class="test-section">
                <div class="test-header">
                    <div class="test-title">📁 文件完整性</div>
                    <div class="test-status status-pending" id="files-status">待检测</div>
                </div>
                <div class="test-body">
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="loader-icon">?</div>
                        <div class="test-description">loader.js 模块加载器</div>
                        <div class="test-result" id="loader-result">检测中...</div>
                    </div>
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="modules-icon">?</div>
                        <div class="test-description">核心模块文件</div>
                        <div class="test-result" id="modules-result">检测中...</div>
                    </div>
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="config-icon">?</div>
                        <div class="test-description">配置文件</div>
                        <div class="test-result" id="config-result">检测中...</div>
                    </div>
                </div>
            </div>
            
            <!-- 功能测试 -->
            <div class="test-section">
                <div class="test-header">
                    <div class="test-title">⚡ 功能测试</div>
                    <div class="test-status status-pending" id="function-status">待测试</div>
                </div>
                <div class="test-body">
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="module-load-icon">?</div>
                        <div class="test-description">模块加载测试</div>
                        <div class="test-result" id="module-load-result">等待中...</div>
                    </div>
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="parse-icon">?</div>
                        <div class="test-description">文本解析测试</div>
                        <div class="test-result" id="parse-result">等待中...</div>
                    </div>
                    <div class="test-item">
                        <div class="test-icon icon-pending" id="storage-test-icon">?</div>
                        <div class="test-description">数据存储测试</div>
                        <div class="test-result" id="storage-test-result">等待中...</div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="btn" onclick="startVerification()">🚀 开始验证</button>
                <button class="btn btn-secondary" onclick="openMainApp()">📱 打开主应用</button>
                <button class="btn btn-secondary" onclick="showLogs()">📋 查看日志</button>
            </div>
            
            <!-- 验证总结 -->
            <div class="summary" id="summary" style="display: none;">
                <h3>📊 验证总结</h3>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="total-tests">0</div>
                        <div class="stat-label">总测试数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="passed-tests">0</div>
                        <div class="stat-label">通过测试</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="failed-tests">0</div>
                        <div class="stat-label">失败测试</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="success-rate">0%</div>
                        <div class="stat-label">成功率</div>
                    </div>
                </div>
            </div>
            
            <!-- 日志输出 -->
            <div class="log-output" id="log-output" style="display: none;"></div>
        </div>
    </div>

    <script>
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            logs: []
        };

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testResults.logs.push(logEntry);
            
            const logOutput = document.getElementById('log-output');
            if (logOutput.style.display !== 'none') {
                logOutput.innerHTML += logEntry + '\n';
                logOutput.scrollTop = logOutput.scrollHeight;
            }
            
            console.log(logEntry);
        }

        function updateTestResult(testId, success, message) {
            testResults.total++;
            if (success) {
                testResults.passed++;
            } else {
                testResults.failed++;
            }
            
            const icon = document.getElementById(testId + '-icon');
            const result = document.getElementById(testId + '-result');
            
            if (success) {
                icon.className = 'test-icon icon-success';
                icon.textContent = '✓';
                result.textContent = message || '通过';
                result.style.color = '#28a745';
            } else {
                icon.className = 'test-icon icon-error';
                icon.textContent = '✗';
                result.textContent = message || '失败';
                result.style.color = '#dc3545';
            }
            
            log(`${testId}: ${success ? '通过' : '失败'} - ${message}`);
        }

        function updateSectionStatus(sectionId, status) {
            const statusElement = document.getElementById(sectionId + '-status');
            statusElement.className = `test-status status-${status}`;
            
            switch (status) {
                case 'running':
                    statusElement.textContent = '检测中';
                    break;
                case 'success':
                    statusElement.textContent = '完成';
                    break;
                case 'error':
                    statusElement.textContent = '异常';
                    break;
                default:
                    statusElement.textContent = '待检测';
            }
        }

        async function startVerification() {
            log('开始部署验证...');
            testResults = { total: 0, passed: 0, failed: 0, logs: [] };
            
            // 环境检测
            await checkEnvironment();
            
            // 依赖检测
            await checkDependencies();
            
            // 文件检测
            await checkFiles();
            
            // 功能测试
            await checkFunctions();
            
            // 显示总结
            showSummary();
            
            log('部署验证完成');
        }

        async function checkEnvironment() {
            updateSectionStatus('env', 'running');
            log('开始环境检测...');
            
            // 浏览器检测
            const userAgent = navigator.userAgent;
            let browserSupported = false;
            let browserInfo = '';
            
            if (userAgent.includes('Chrome')) {
                const version = userAgent.match(/Chrome\/(\d+)/);
                browserSupported = version && parseInt(version[1]) >= 80;
                browserInfo = `Chrome ${version ? version[1] : 'Unknown'}`;
            } else if (userAgent.includes('Firefox')) {
                const version = userAgent.match(/Firefox\/(\d+)/);
                browserSupported = version && parseInt(version[1]) >= 75;
                browserInfo = `Firefox ${version ? version[1] : 'Unknown'}`;
            } else if (userAgent.includes('Safari')) {
                browserSupported = true; // Safari 13+ 通常支持
                browserInfo = 'Safari';
            } else if (userAgent.includes('Edge')) {
                const version = userAgent.match(/Edg\/(\d+)/);
                browserSupported = version && parseInt(version[1]) >= 80;
                browserInfo = `Edge ${version ? version[1] : 'Unknown'}`;
            }
            
            updateTestResult('browser', browserSupported, browserInfo);
            
            // 协议检测
            const isFileProtocol = location.protocol === 'file:';
            updateTestResult('protocol', true, isFileProtocol ? 'file://' : location.protocol);
            
            // localStorage检测
            let storageSupported = false;
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                storageSupported = true;
            } catch (e) {
                // localStorage不可用
            }
            updateTestResult('storage', storageSupported, storageSupported ? '可用' : '不可用');
            
            // ES6模块检测
            const es6Supported = typeof Symbol !== 'undefined' && typeof Promise !== 'undefined';
            updateTestResult('es6', es6Supported, es6Supported ? '支持' : '不支持');
            
            updateSectionStatus('env', 'success');
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        async function checkDependencies() {
            updateSectionStatus('deps', 'running');
            log('开始依赖检测...');
            
            // ECharts检测
            const echartsLoaded = typeof echarts !== 'undefined';
            updateTestResult('echarts', echartsLoaded, echartsLoaded ? `v${echarts.version || 'Unknown'}` : '未加载');
            
            // Papa Parse检测
            const papaLoaded = typeof Papa !== 'undefined';
            updateTestResult('papa', papaLoaded, papaLoaded ? `v${Papa.version || 'Unknown'}` : '未加载');
            
            // Font Awesome检测
            const faLoaded = document.querySelector('link[href*="font-awesome"]') !== null;
            updateTestResult('fontawesome', faLoaded, faLoaded ? '已加载' : '未加载');
            
            updateSectionStatus('deps', 'success');
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        async function checkFiles() {
            updateSectionStatus('files', 'running');
            log('开始文件检测...');
            
            // 检查loader.js
            try {
                const response = await fetch('./loader.js');
                const loaderExists = response.ok;
                updateTestResult('loader', loaderExists, loaderExists ? '存在' : '缺失');
            } catch (e) {
                updateTestResult('loader', false, '无法访问');
            }
            
            // 检查核心模块
            const modules = ['constants.js', 'storage.js', 'parser.js', 'charts.js', 'main.js'];
            let moduleCount = 0;
            
            for (const module of modules) {
                try {
                    const response = await fetch(`./src/${module}`);
                    if (response.ok) moduleCount++;
                } catch (e) {
                    // 文件不存在
                }
            }
            
            const modulesComplete = moduleCount === modules.length;
            updateTestResult('modules', modulesComplete, `${moduleCount}/${modules.length} 个模块`);
            
            // 检查配置文件
            try {
                const response = await fetch('./config/local-config.example.js');
                const configExists = response.ok;
                updateTestResult('config', configExists, configExists ? '配置模板存在' : '配置模板缺失');
            } catch (e) {
                updateTestResult('config', false, '无法访问配置');
            }
            
            updateSectionStatus('files', 'success');
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        async function checkFunctions() {
            updateSectionStatus('function', 'running');
            log('开始功能测试...');
            
            // 模块加载测试
            const moduleLoadTest = window.ModuleExports && Object.keys(window.ModuleExports).length > 0;
            updateTestResult('module-load', moduleLoadTest, moduleLoadTest ? '模块已加载' : '模块未加载');
            
            // 解析测试
            let parseTest = false;
            if (window.ModuleExports && window.ModuleExports['parser.js']) {
                try {
                    const parser = window.ModuleExports['parser.js'];
                    const testResult = parser.parseTimestamp('02/01/2025', '13:51:11');
                    parseTest = testResult > 0;
                } catch (e) {
                    parseTest = false;
                }
            }
            updateTestResult('parse', parseTest, parseTest ? '解析功能正常' : '解析功能异常');
            
            // 存储测试
            let storageTestResult = false;
            if (window.ModuleExports && window.ModuleExports['storage.js']) {
                try {
                    const storage = window.ModuleExports['storage.js'];
                    const testData = { test: 'deployment-verification' };
                    const saved = storage.save('test-key', testData);
                    const loaded = storage.load('test-key');
                    storage.remove('test-key');
                    storageTestResult = saved && loaded && loaded.test === 'deployment-verification';
                } catch (e) {
                    storageTestResult = false;
                }
            }
            updateTestResult('storage-test', storageTestResult, storageTestResult ? '存储功能正常' : '存储功能异常');
            
            updateSectionStatus('function', 'success');
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        function showSummary() {
            const summary = document.getElementById('summary');
            summary.style.display = 'block';
            
            document.getElementById('total-tests').textContent = testResults.total;
            document.getElementById('passed-tests').textContent = testResults.passed;
            document.getElementById('failed-tests').textContent = testResults.failed;
            
            const successRate = testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
            
            log(`验证完成: ${testResults.passed}/${testResults.total} 通过 (${successRate}%)`);
        }

        function openMainApp() {
            window.open('./index.html', '_blank');
        }

        function showLogs() {
            const logOutput = document.getElementById('log-output');
            if (logOutput.style.display === 'none') {
                logOutput.style.display = 'block';
                logOutput.innerHTML = testResults.logs.join('\n');
                logOutput.scrollTop = logOutput.scrollHeight;
            } else {
                logOutput.style.display = 'none';
            }
        }

        // 页面加载完成后自动开始验证
        window.addEventListener('load', () => {
            setTimeout(startVerification, 1000);
        });
    </script>
</body>
</html>
