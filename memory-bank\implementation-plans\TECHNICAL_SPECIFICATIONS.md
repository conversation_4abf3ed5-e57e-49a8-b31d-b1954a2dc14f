# 📋 技术规格说明书 (Technical Specifications)

## 🎯 项目规格概览

### 📊 项目指标
| 指标类型 | 当前状态 | 目标状态 | 改进幅度 |
|---------|---------|---------|---------|
| 代码行数 | 31,000+ 行 | 8个模块，每个<2,000行 | 可维护性提升 80% |
| 加载时间 | 一次性全加载 | 按需动态加载 | 启动速度提升 60% |
| 内存使用 | 单体应用高占用 | 模块化内存管理 | 内存效率提升 40% |
| 开发效率 | 难以定位问题 | 模块化开发调试 | 开发效率提升 70% |
| 部署复杂度 | 单文件依赖 | 零服务器部署 | 部署成本降低 90% |

### 🏗️ 系统架构规格

#### 核心架构模式
```
模式名称: 零服务器动态模块化架构 (Zero-Server Dynamic Modular Architecture)
```

**设计原则:**
- **零依赖**: 不依赖 npm、webpack 等构建工具
- **零服务器**: 支持 file:// 协议直接运行
- **模块化**: ES6 Modules 标准化模块系统
- **向后兼容**: 保持所有现有功能特性
- **性能优先**: 动态加载和并发控制

#### 技术栈规格
```yaml
运行环境:
  - 协议支持: file://, http://, https://
  - 浏览器要求: Chrome 88+, Firefox 78+, Safari 14+, Edge 88+
  
核心技术:
  - 模块系统: ES6 Modules with dynamic imports
  - 库管理: CDN-based external libraries
  - 数据存储: localStorage Web API
  - 并发控制: Custom TaskPool implementation
  
外部依赖:
  - ECharts: 5.x (图表渲染引擎)
  - PapaParse: 5.x (CSV处理引擎) 
  - Kimi API: moonshot-v1 (AI分析服务)
```

## 📁 文件架构规格

### 🗂️ 目录结构定义
```
refactored/solution1-es6-modules/
├── index.html              # 🏠 应用入口 (1KB)
├── loader.js                # 🔄 模块加载器 (3KB)
├── src/
│   ├── constants.js         # ⚙️ 全局配置 (1KB)
│   ├── storage.js           # 💾 数据层 (8KB)
│   ├── parser.js            # 📝 解析层 (12KB)
│   ├── charts.js            # 📊 图表层 (6KB)
│   ├── drag-upload.js       # 📁 上传层 (4KB)
│   └── main.js              # 🎯 应用层 (8KB)
├── config/
│   ├── local-config.example.js  # 🔑 配置模板
│   └── local-config.js      # 🔐 用户密钥 (gitignore)
└── docs/
    ├── SOLUTION_PLAN.md     # 📋 方案计划书
    ├── ARCHITECTURE_DIAGRAMS.md  # 🏗️ 架构图表
    └── IMPLEMENTATION_GUIDE.md   # 🚀 实施指南
```

### 📋 文件规格定义

#### 1. index.html (Entry Point)
```html
规格要求:
- 大小限制: < 1KB
- 职责范围: DOM结构定义, CDN库加载, 模块启动
- 依赖关系: ECharts CDN, PapaParse CDN, loader.js
- 兼容性: HTML5标准, 支持ES6模块

关键元素:
- DOM容器: file-input, start-btn, export-btn, chart-*
- CDN引用: echarts.min.js, papaparse.min.js  
- 模块入口: <script type="module" src="loader.js">
```

#### 2. loader.js (Module Loader)
```javascript
规格要求:
- 大小限制: < 3KB
- 职责范围: ES模块动态加载, 依赖图管理, 错误处理
- 加载顺序: constants → [storage,parser,charts,upload] → main
- 错误策略: 失败回退到 standalone.html

核心功能:
- loadModule(modulePath): Promise<Module>
- transformESMSyntax(code): string
- buildDependencyGraph(): Map<string, string[]>
- handleLoadError(error, modulePath): void
```

#### 3. constants.js (Configuration Layer)
```javascript
规格要求:
- 大小限制: < 1KB
- 职责范围: 全局常量定义, 配置中心化
- 导出接口: MAX_CONCURRENCY, API_ENDPOINTS, STORAGE_KEYS
- 修改策略: 只增加不删除已有常量

导出规格:
export const MAX_CONCURRENCY = 50;
export const API_TIMEOUT = 90000;
export const STORAGE_PREFIX = 'qna_';
export const API_ENDPOINTS = { KIMI: 'https://api.moonshot.cn/v1/chat/completions' };
```

#### 4. storage.js (Data Persistence Layer)
```javascript
规格要求:
- 大小限制: < 8KB
- 职责范围: localStorage管理, 数据序列化, CSV导出
- 数据格式: JSON序列化, UTF-8编码
- 迁移支持: enhanced_qna_* → qna_* 自动迁移

核心类规格:
class AppDataManager {
  // 数据操作
  saveDriverData(data): void
  loadDriverData(): Array<Object>
  
  // 导出功能  
  exportToCSV(type): string
  exportAllData(): Object
  
  // 清理功能
  clearAllData(): void
  migrateOldData(): boolean
}
```

#### 5. parser.js (Text Processing Layer)
```javascript
规格要求:
- 大小限制: < 12KB
- 职责范围: 文本解析, AI调用, 对话分组
- 解析格式: "HH:MM Driver|Support Name: Message"
- AI超时: 90秒, 支持重试机制

核心函数规格:
// 文本解析
parseTxtContent(content: string, fileName: string): Object

// AI分析  
evaluateConversationWithKimi(conversation: Object, apiKey: string): Promise<Object>

// 对话分组
groupConversationsByQuestion(messages: Array): Array<Object>
```

#### 6. charts.js (Visualization Layer)
```javascript
规格要求:
- 大小限制: < 6KB
- 职责范围: ECharts初始化, 数据更新, 图表配置
- 图表类型: 4种图表 (问题分布, 有效性, 满意度, 知识点)
- 响应式: 支持窗口大小变化

核心函数规格:
// 初始化
initializeCharts(): void

// 数据更新
updateChartsData(data: Object): void

// 图表管理
clearCharts(): void
resizeCharts(): void
```

#### 7. drag-upload.js (File Upload Layer)
```javascript
规格要求:
- 大小限制: < 4KB
- 职责范围: 文件拖拽, 上传处理, 进度显示
- 文件类型: .txt文件, 支持多选
- 事件绑定: drag & drop, file input change

核心功能规格:
// 初始化上传
initializeUpload(): void

// 文件处理
handleFileSelect(files: FileList): void
handleDragAndDrop(event: DragEvent): void

// 进度管理
updateProgress(current: number, total: number): void
```

#### 8. main.js (Application Layer)
```javascript
规格要求:
- 大小限制: < 8KB
- 职责范围: 应用编排, 业务流程, TaskPool管理
- 并发控制: MAX_CONCURRENCY=50
- 状态管理: 应用生命周期管理

核心功能规格:
// 应用启动
initializeApp(): Promise<void>

// 文件处理
processFiles(files: FileList): Promise<void>

// 任务管理
class TaskPool {
  constructor(maxConcurrency: number)
  addTask(task: Function): Promise<any>
  waitAll(): Promise<Array>
}
```

## 🔧 技术契约 (API Contracts)

### 📡 模块间通信协议

#### 数据流契约
```javascript
// 文件处理流程
FileInput → parser.parseTxtContent() → AI分析 → storage.saveData() → charts.updateCharts()

// 数据格式标准
interface ConversationData {
  fileName: string;
  timestamp: number;
  messages: Array<{
    time: string;
    speaker: string;
    content: string;
    type: 'driver' | 'support';
  }>;
  analysis?: {
    effectiveness: number;     // 1-5
    satisfaction: number;      // 1-5  
    knowledge_covered: number; // 1-5
    question_category: string;
  };
}
```

#### 存储契约
```javascript
// localStorage 键值规范
const STORAGE_KEYS = {
  DRIVERS: 'qna_drivers',
  CUSTOMER_SERVICE: 'qna_customer_service', 
  KNOWLEDGE: 'qna_knowledge',
  METRICS: 'qna_metrics',
  CATEGORIES: 'qna_question_categories',
  SETTINGS: 'qna_settings'
};

// 数据版本管理
interface StorageData {
  version: string;          // "2.0.0"
  timestamp: number;        // Unix timestamp
  data: ConversationData[]; // 实际数据
}
```

#### 错误处理契约
```javascript
// 统一错误格式
interface AppError {
  type: 'PARSE_ERROR' | 'API_ERROR' | 'STORAGE_ERROR' | 'CHART_ERROR';
  message: string;
  fileName?: string;
  stack?: string;
  timestamp: number;
}

// 错误处理流程
try {
  // 业务逻辑
} catch (error) {
  logError(error);
  showUserFriendlyMessage(error);
  reportToConsole(error);
}
```

## 🚀 性能规格要求

### ⏱️ 响应时间要求
| 操作类型 | 目标时间 | 最大允许时间 | 测量方法 |
|---------|---------|-------------|---------|
| 模块加载 | < 2秒 | < 5秒 | performance.now() |
| 单文件解析 | < 5秒 | < 15秒 | 处理时间戳 |
| AI分析响应 | < 30秒 | < 90秒 | API调用计时 |
| 图表渲染 | < 1秒 | < 3秒 | ECharts回调 |
| CSV导出 | < 2秒 | < 10秒 | 下载开始时间 |

### 💾 内存使用规格
```javascript
// 内存使用监控
const MEMORY_LIMITS = {
  SINGLE_FILE_MAX: 50 * 1024 * 1024,      // 50MB per file
  TOTAL_HEAP_MAX: 500 * 1024 * 1024,      // 500MB total heap
  CHART_DATA_MAX: 100 * 1024 * 1024,      // 100MB for charts
  CACHE_SIZE_MAX: 50 * 1024 * 1024        // 50MB for cache
};

// 内存清理策略
function performMemoryCleanup() {
  // 清理临时数据
  window.tempParseResults = null;
  
  // 清理图表实例
  chartInstances.forEach(chart => chart.dispose());
  
  // 触发垃圾回收提示
  if (window.gc) window.gc();
}
```

### 🔄 并发处理规格
```javascript
// 并发控制参数
const CONCURRENCY_CONFIG = {
  MAX_CONCURRENT_FILES: 50,           // 最大并发文件数
  MAX_CONCURRENT_API_CALLS: 10,      // 最大并发API调用
  RETRY_ATTEMPTS: 3,                 // 重试次数
  RETRY_DELAY: 1000,                 // 重试延迟(ms)
  TIMEOUT_MS: 90000                  // 超时时间(ms)
};

// TaskPool 实现规格
class TaskPool {
  constructor(maxConcurrency = 50) {
    this.maxConcurrency = maxConcurrency;
    this.running = new Set();
    this.queue = [];
  }
  
  async addTask(taskFn) {
    return new Promise((resolve, reject) => {
      this.queue.push({ taskFn, resolve, reject });
      this.processQueue();
    });
  }
}
```

## 🔒 安全规格要求

### 🔐 API密钥管理
```javascript
// 密钥存储规范
const API_KEY_RULES = {
  STORAGE_LOCATION: 'config/local-config.js',  // 本地文件
  VERSION_CONTROL: false,                       // 不纳入版本控制
  VALIDATION_PATTERN: /^sk-[a-zA-Z0-9]{32,}$/, // 格式验证
  ACCESS_METHOD: 'window.LOCAL_CONFIG.apiKey'   // 访问方式
};

// 安全检查
function validateApiKey(apiKey) {
  if (!apiKey || typeof apiKey !== 'string') {
    throw new Error('API密钥未配置或格式错误');
  }
  
  if (!API_KEY_RULES.VALIDATION_PATTERN.test(apiKey)) {
    throw new Error('API密钥格式不符合要求');
  }
  
  return true;
}
```

### 🛡️ 数据安全
```javascript
// 数据处理安全规范
const DATA_SECURITY_RULES = {
  // 输入验证
  FILE_SIZE_LIMIT: 10 * 1024 * 1024,        // 10MB文件大小限制
  FILE_TYPE_WHITELIST: ['.txt'],            // 允许的文件类型
  CONTENT_SANITIZATION: true,               // 内容清理
  
  // 存储安全
  STORAGE_ENCRYPTION: false,                // localStorage不加密
  DATA_RETENTION: 'user_controlled',        // 用户控制数据保留
  
  // 传输安全
  API_HTTPS_ONLY: true,                     // 仅HTTPS API调用
  NO_SENSITIVE_LOGGING: true                // 不记录敏感信息
};
```

## 📊 质量保证规格

### 🧪 测试覆盖要求
```
测试类型              最低覆盖率    验证方法
===================  ==========   ==============
功能测试              100%         手动执行测试用例
错误处理测试          100%         异常场景验证  
兼容性测试            95%          多浏览器测试
性能测试              90%          基准测试通过
集成测试              95%          端到端流程测试
```

### 📋 代码质量指标
```javascript
// 代码质量检查点
const CODE_QUALITY_METRICS = {
  // 文件大小控制
  MAX_FILE_SIZE: 12 * 1024,           // 12KB per file
  MAX_FUNCTION_LENGTH: 50,            // 50 lines per function
  MAX_COMPLEXITY: 10,                 // Cyclomatic complexity
  
  // 代码风格
  NAMING_CONVENTION: 'camelCase',     // 驼峰命名
  INDENTATION: 2,                     // 2空格缩进
  MAX_LINE_LENGTH: 100,               // 100字符行宽
  
  // 文档要求
  MIN_COMMENT_RATIO: 0.2,             // 20%注释率
  FUNCTION_DOCUMENTATION: true,        // 函数文档必需
  API_DOCUMENTATION: true              // API文档必需
};
```

## 🔧 部署规格要求

### 📦 打包规格
```bash
# 文件结构验证
required_files = [
  "index.html",
  "loader.js", 
  "src/constants.js",
  "src/storage.js",
  "src/parser.js", 
  "src/charts.js",
  "src/drag-upload.js",
  "src/main.js",
  "config/local-config.example.js"
]

# 大小限制验证
max_total_size = 100 * 1024  # 100KB total
max_individual_size = 12 * 1024  # 12KB per file
```

### 🌐 运行环境规格
```yaml
运行要求:
  协议支持:
    - file://    ✓ 主要部署方式
    - http://    ✓ 本地服务器
    - https://   ✓ 生产环境
    
  浏览器支持:
    - Chrome 88+     ✓ ES6 Modules
    - Firefox 78+    ✓ Dynamic imports  
    - Safari 14+     ✓ 完整支持
    - Edge 88+       ✓ Chromium基础
    
  性能要求:
    - RAM: 512MB+    ✓ 基本运行
    - CPU: 2核+      ✓ 并发处理
    - 网络: 1Mbps+   ✓ API调用
```

## 📈 监控与维护规格

### 📊 运行时监控
```javascript
// 性能监控数据收集
const MONITORING_CONFIG = {
  METRICS_COLLECTION: {
    loadTime: true,           // 加载时间
    memoryUsage: true,        // 内存使用
    apiLatency: true,         // API延迟
    errorRate: true,          // 错误率
    throughput: true          // 处理吞吐量
  },
  
  REPORTING_INTERVAL: 60000,  // 60秒报告间隔
  LOG_LEVEL: 'INFO',          // 日志级别
  RETENTION_DAYS: 7           // 日志保留天数
};

// 健康检查
function performHealthCheck() {
  const health = {
    moduleStatus: checkModuleLoad(),
    apiConnectivity: checkApiAccess(), 
    storageAvailable: checkLocalStorage(),
    memoryUsage: getMemoryUsage(),
    timestamp: Date.now()
  };
  
  return health;
}
```

### 🔄 维护计划
```
维护类型          频率        责任方      检查内容
==============   =======    ========   ================
功能验证          每月       开发团队    核心功能正常
性能监控          每周       运维团队    性能指标达标  
安全检查          每季度     安全团队    漏洞扫描修复
依赖更新          每半年     开发团队    CDN库版本更新
文档更新          即时       产品团队    使用说明维护
```

---

**📋 此技术规格说明书定义了零服务器模块化方案的完整技术标准，确保项目实施过程中的技术一致性和质量保证。**
