/**
 * 动态模块加载器 - 支持file://协议的ES6模块加载
 * <AUTHOR> Agent
 * @version 1.0.0
 */

(function() {
    'use strict';

    // 模块依赖图 - 定义模块加载顺序（优化版本）
    const MODULE_GRAPH = {
        // 基础模块 - 第一层
        'constants.js': [],
        'utils.js': [],

        // 核心基础设施 - 第二层
        'service-container.js': ['utils.js'],
        'event-bus.js': ['utils.js'],
        'dependency-manager.js': ['utils.js'],
        'performance-monitor.js': ['utils.js'],

        // 数据处理系统 - 第三层
        'storage-manager.js': ['utils.js', 'constants.js'],
        'data-processor.js': ['utils.js', 'storage-manager.js'],

        // 原有核心模块 - 第四层
        'storage.js': ['constants.js', 'storage-manager.js'],
        'parser.js': ['constants.js', 'utils.js'],
        'charts.js': ['constants.js', 'utils.js'],
        'drag-upload.js': ['utils.js'],

        // UI组件系统 - 第五层
        'notification.js': ['utils.js'],
        'modal.js': ['utils.js'],
        'tabs.js': ['utils.js'],
        'progress.js': ['utils.js'],

        // 高级功能系统 - 第六层
        'enhanced-upload.js': ['utils.js', 'storage-manager.js', 'progress.js'],
        'qa-optimization.js': ['utils.js', 'data-processor.js'],
        'report-generator.js': ['utils.js', 'data-processor.js', 'progress.js'],
        'tag-center.js': ['utils.js'],

        // 模块加载优化器 - 第七层
        'module-loader-optimizer.js': ['utils.js', 'performance-monitor.js'],

        // UI和主应用 - 第八层
        'ui.js': ['notification.js', 'modal.js', 'tabs.js', 'progress.js'],
        'main.js': [
            'constants.js', 'utils.js', 'service-container.js', 'event-bus.js',
            'storage.js', 'parser.js', 'charts.js', 'drag-upload.js', 'ui.js',
            'notification.js', 'modal.js', 'tabs.js', 'progress.js',
            'enhanced-upload.js', 'qa-optimization.js', 'report-generator.js',
            'module-loader-optimizer.js'
        ]
    };

    // 模块基础路径
    const MODULE_BASE_PATH = './src/';

    // 懒加载模块配置（优化版本）
    const LAZY_MODULES = {
        // 核心UI组件 - 总是需要
        'notification.js': () => true,
        'modal.js': () => true,
        'progress.js': () => true,

        // 条件加载的UI组件
        'tabs.js': () => document.querySelector('.tabs-container') !== null,
        'charts.js': () => document.getElementById('chart-questions') !== null,

        // 文件处理相关
        'drag-upload.js': () => document.getElementById('drop-zone') !== null,
        'enhanced-upload.js': () => document.getElementById('drop-zone') !== null,

        // 高级功能 - 按需加载
        'qa-optimization.js': () => document.querySelector('.qa-dataset-panel') !== null,
        'report-generator.js': () => document.querySelector('.reports-panel') !== null,
        'tag-center.js': () => document.querySelector('.tag-management') !== null,

        // 优化器 - 开发模式下加载
        'module-loader-optimizer.js': () => window.DEBUG_MODE || false
    };

    // 已加载的模块缓存
    const loadedModules = new Set();
    const loadingModules = new Map();
    const moduleCache = new Map();
    
    // 错误处理
    let errorCount = 0;
    const MAX_ERRORS = 3;

    /**
     * 日志记录函数
     */
    function log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] [Loader] ${message}`);

        // 更新页面加载进度
        updateLoadingProgress(message);

        // 可选：显示在页面上
        if (type === 'error') {
            console.error(`[${timestamp}] [Loader] ERROR: ${message}`);
        }
    }

    /**
     * 更新加载进度显示
     */
    function updateLoadingProgress(message) {
        const progressElement = document.getElementById('loading-progress');
        if (progressElement) {
            progressElement.textContent = message;
        }
    }

    /**
     * 转换ES6模块语法为普通JavaScript
     */
    function transformESMSyntax(code, moduleName) {
        // 移除import语句（依赖已通过加载顺序处理）
        code = code.replace(/import\s+.*?from\s+['"][^'"]*['"];?\s*/g, '');
        
        // 转换export语句
        code = code.replace(/export\s+default\s+/g, 'window.ModuleExports = window.ModuleExports || {}; window.ModuleExports["' + moduleName + '"] = ');
        code = code.replace(/export\s+\{([^}]+)\}/g, function(match, exports) {
            const exportList = exports.split(',').map(e => e.trim());
            let result = 'window.ModuleExports = window.ModuleExports || {}; window.ModuleExports["' + moduleName + '"] = {';
            result += exportList.map(exp => `${exp}: ${exp}`).join(', ');
            result += '};';
            return result;
        });
        
        // 转换具名导出
        code = code.replace(/export\s+(const|let|var|function|class)\s+(\w+)/g, function(match, type, name) {
            return `${type} ${name}`;
        });
        
        return code;
    }

    /**
     * 加载单个模块
     */
    async function loadModule(moduleName) {
        if (loadedModules.has(moduleName)) {
            return Promise.resolve();
        }

        if (loadingModules.has(moduleName)) {
            return loadingModules.get(moduleName);
        }

        const loadPromise = (async () => {
            try {
                log(`开始加载模块: ${moduleName}`);
                
                const modulePath = MODULE_BASE_PATH + moduleName;
                const response = await fetch(modulePath);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const code = await response.text();
                const transformedCode = transformESMSyntax(code, moduleName);
                
                // 创建并执行脚本
                const script = document.createElement('script');
                script.textContent = transformedCode;
                script.setAttribute('data-module', moduleName);
                
                // 错误处理
                script.onerror = () => {
                    throw new Error(`脚本执行失败: ${moduleName}`);
                };
                
                document.head.appendChild(script);
                loadedModules.add(moduleName);
                
                log(`模块加载成功: ${moduleName}`);
                
            } catch (error) {
                errorCount++;
                log(`模块加载失败: ${moduleName} - ${error.message}`, 'error');
                throw error;
            }
        })();

        loadingModules.set(moduleName, loadPromise);
        return loadPromise;
    }

    /**
     * 递归加载模块及其依赖
     */
    async function loadModuleWithDependencies(moduleName) {
        const dependencies = MODULE_GRAPH[moduleName] || [];

        // 先加载所有依赖
        for (const dep of dependencies) {
            await loadModuleWithDependencies(dep);
        }

        // 再加载当前模块
        await loadModule(moduleName);
    }

    /**
     * 优化的模块加载顺序
     */
    async function loadModulesInOptimalOrder(modules) {
        if (modules.length === 0) return;

        // 按依赖层级分组
        const levels = new Map();
        const processed = new Set();

        const calculateLevel = (module, level = 0) => {
            if (processed.has(module)) {
                return levels.get(module) || 0;
            }

            processed.add(module);
            const dependencies = MODULE_GRAPH[module] || [];

            let maxDepLevel = level;
            for (const dep of dependencies) {
                if (modules.includes(dep)) {
                    maxDepLevel = Math.max(maxDepLevel, calculateLevel(dep, level) + 1);
                }
            }

            levels.set(module, maxDepLevel);
            return maxDepLevel;
        };

        // 计算每个模块的层级
        modules.forEach(module => calculateLevel(module));

        // 按层级分组
        const levelGroups = new Map();
        for (const [module, level] of levels) {
            if (!levelGroups.has(level)) {
                levelGroups.set(level, []);
            }
            levelGroups.get(level).push(module);
        }

        // 按层级顺序加载
        const sortedLevels = Array.from(levelGroups.keys()).sort((a, b) => a - b);
        for (const level of sortedLevels) {
            const levelModules = levelGroups.get(level);
            log(`📦 加载第 ${level} 层模块: ${levelModules.join(', ')}`);

            // 同一层级的模块可以并行加载
            await Promise.all(levelModules.map(module => loadModule(module)));
        }
    }

    /**
     * 加载所有模块（优化版本）
     */
    async function loadAllModules() {
        try {
            log('🚀 开始优化模块加载...');

            // 分离核心模块和懒加载模块
            const moduleNames = Object.keys(MODULE_GRAPH);
            const coreModules = moduleNames.filter(name => !LAZY_MODULES[name]);
            const lazyModules = moduleNames.filter(name => LAZY_MODULES[name]);

            log(`📦 核心模块: ${coreModules.length} 个`);
            log(`⏳ 懒加载模块: ${lazyModules.length} 个`);

            // 使用优化的批量加载
            await loadModulesInOptimalOrder(coreModules);

            // 检查懒加载模块是否需要立即加载
            const immediatelyNeededLazy = [];
            for (const moduleName of lazyModules) {
                const shouldLoad = LAZY_MODULES[moduleName];
                if (shouldLoad && shouldLoad()) {
                    immediatelyNeededLazy.push(moduleName);
                }
            }

            if (immediatelyNeededLazy.length > 0) {
                log(`🔄 立即加载懒加载模块: ${immediatelyNeededLazy.join(', ')}`);
                await loadModulesInOptimalOrder(immediatelyNeededLazy);
            }

            log('核心模块加载完成');
            
            // 显示模块状态
            showModuleStatus();

            // 设置集成测试
            setupIntegrationTests();

            // 隐藏加载界面
            const loadingElement = document.getElementById('loading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }

            // 显示测试面板
            const moduleStatusElement = document.getElementById('module-status');
            const integrationTestElement = document.getElementById('integration-test');
            if (moduleStatusElement) moduleStatusElement.style.display = 'block';
            if (integrationTestElement) integrationTestElement.style.display = 'block';

            // 初始化主应用
            if (window.ModuleExports && window.ModuleExports['main.js']) {
                const mainModule = window.ModuleExports['main.js'];
                if (typeof mainModule.init === 'function') {
                    mainModule.init();
                } else if (typeof mainModule === 'function') {
                    mainModule();
                }
            }
            
        } catch (error) {
            log(`模块加载失败: ${error.message}`, 'error');
            
            if (errorCount >= MAX_ERRORS) {
                log('错误次数过多，尝试回退到standalone版本', 'error');
                fallbackToStandalone();
            }
        }
    }

    /**
     * 懒加载模块
     */
    async function loadLazyModule(moduleName) {
        if (loadedModules.has(moduleName)) {
            return window.ModuleExports[moduleName];
        }

        if (LAZY_MODULES[moduleName]) {
            log(`懒加载模块: ${moduleName}`);
            return await loadModuleWithDependencies(moduleName);
        }

        throw new Error(`模块 ${moduleName} 未配置懒加载`);
    }

    /**
     * 暴露懒加载函数到全局
     */
    window.loadLazyModule = loadLazyModule;

    /**
     * 显示模块状态
     */
    function showModuleStatus() {
        const moduleListElement = document.getElementById('module-list');
        if (!moduleListElement) return;

        const moduleNames = Object.keys(MODULE_GRAPH);
        let statusHTML = '';

        moduleNames.forEach(moduleName => {
            const isLoaded = window.ModuleExports && window.ModuleExports[moduleName];
            const status = isLoaded ? 'success' : 'error';
            const icon = isLoaded ? 'fa-check-circle' : 'fa-times-circle';
            const color = isLoaded ? '#28a745' : '#dc3545';

            statusHTML += `
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 4px; text-align: center;">
                    <i class="fas ${icon}" style="color: ${color}; font-size: 20px; margin-bottom: 5px;"></i>
                    <div style="font-size: 12px; font-weight: bold;">${moduleName}</div>
                    <div style="font-size: 10px; color: #666;">${isLoaded ? '已加载' : '加载失败'}</div>
                </div>
            `;
        });

        moduleListElement.innerHTML = statusHTML;
    }

    /**
     * 设置集成测试
     */
    function setupIntegrationTests() {
        const testModulesBtn = document.getElementById('test-modules');
        const testDependenciesBtn = document.getElementById('test-dependencies');
        const testStorageBtn = document.getElementById('test-storage');
        const testParserBtn = document.getElementById('test-parser');
        const testResultsElement = document.getElementById('test-results');

        if (testModulesBtn) {
            testModulesBtn.addEventListener('click', () => {
                runModuleInterfaceTests(testResultsElement);
            });
        }

        if (testDependenciesBtn) {
            testDependenciesBtn.addEventListener('click', () => {
                runDependencyTests(testResultsElement);
            });
        }

        if (testStorageBtn) {
            testStorageBtn.addEventListener('click', () => {
                runStorageTests(testResultsElement);
            });
        }

        if (testParserBtn) {
            testParserBtn.addEventListener('click', () => {
                loadAndRunParserTests(testResultsElement);
            });
        }
    }

    /**
     * 运行模块接口测试
     */
    function runModuleInterfaceTests(resultElement) {
        if (!resultElement) return;

        let results = '=== 模块接口测试 ===\n';
        let passCount = 0;
        let totalCount = 0;

        const tests = [
            {
                name: 'constants.js 导出测试',
                test: () => window.CONSTANTS && typeof window.CONSTANTS.MAX_CONCURRENCY === 'number'
            },
            {
                name: 'storage.js AppDataManager 测试',
                test: () => window.ModuleExports && window.ModuleExports['storage.js'] &&
                           typeof window.ModuleExports['storage.js'].save === 'function'
            },
            {
                name: 'parser.js TextParser 测试',
                test: () => window.ModuleExports && window.ModuleExports['parser.js'] &&
                           typeof window.ModuleExports['parser.js'].parseTxtContent === 'function'
            },
            {
                name: 'charts.js ChartManager 测试',
                test: () => window.ModuleExports && window.ModuleExports['charts.js'] &&
                           typeof window.ModuleExports['charts.js'].initChart === 'function'
            },
            {
                name: 'main.js MainApp 测试',
                test: () => window.ModuleExports && window.ModuleExports['main.js'] &&
                           typeof window.ModuleExports['main.js'].init === 'function'
            }
        ];

        tests.forEach(test => {
            totalCount++;
            try {
                const passed = test.test();
                if (passed) {
                    passCount++;
                    results += `✅ ${test.name}: PASS\n`;
                } else {
                    results += `❌ ${test.name}: FAIL\n`;
                }
            } catch (error) {
                results += `❌ ${test.name}: ERROR - ${error.message}\n`;
            }
        });

        results += `\n总计: ${passCount}/${totalCount} 通过\n\n`;
        resultElement.textContent = results;
    }

    /**
     * 运行依赖关系测试
     */
    function runDependencyTests(resultElement) {
        if (!resultElement) return;

        let results = '=== 依赖关系测试 ===\n';
        let passCount = 0;
        let totalCount = 0;

        // 测试模块依赖关系
        Object.entries(MODULE_GRAPH).forEach(([moduleName, dependencies]) => {
            totalCount++;
            let allDepsLoaded = true;
            let missingDeps = [];

            dependencies.forEach(dep => {
                if (!window.ModuleExports || !window.ModuleExports[dep]) {
                    allDepsLoaded = false;
                    missingDeps.push(dep);
                }
            });

            if (allDepsLoaded) {
                passCount++;
                results += `✅ ${moduleName}: 所有依赖已加载\n`;
            } else {
                results += `❌ ${moduleName}: 缺少依赖 [${missingDeps.join(', ')}]\n`;
            }
        });

        results += `\n总计: ${passCount}/${totalCount} 通过\n\n`;
        resultElement.textContent = results;
    }

    /**
     * 运行存储功能测试
     */
    function runStorageTests(resultElement) {
        if (!resultElement) return;

        let results = '=== 存储功能测试 ===\n';
        let passCount = 0;
        let totalCount = 0;

        const storageModule = window.ModuleExports && window.ModuleExports['storage.js'];

        if (!storageModule) {
            results += '❌ 存储模块未加载\n';
            resultElement.textContent = results;
            return;
        }

        const tests = [
            {
                name: 'localStorage 可用性测试',
                test: () => {
                    try {
                        localStorage.setItem('test', 'test');
                        localStorage.removeItem('test');
                        return true;
                    } catch (e) {
                        return false;
                    }
                }
            },
            {
                name: '数据保存测试',
                test: () => {
                    const testData = { test: 'data', timestamp: Date.now() };
                    return storageModule.save('test_key', testData);
                }
            },
            {
                name: '数据加载测试',
                test: () => {
                    const loadedData = storageModule.load('test_key');
                    return loadedData && loadedData.test === 'data';
                }
            },
            {
                name: '数据删除测试',
                test: () => {
                    const result = storageModule.remove('test_key');
                    const shouldBeNull = storageModule.load('test_key');
                    return result && shouldBeNull === null;
                }
            }
        ];

        tests.forEach(test => {
            totalCount++;
            try {
                const passed = test.test();
                if (passed) {
                    passCount++;
                    results += `✅ ${test.name}: PASS\n`;
                } else {
                    results += `❌ ${test.name}: FAIL\n`;
                }
            } catch (error) {
                results += `❌ ${test.name}: ERROR - ${error.message}\n`;
            }
        });

        results += `\n总计: ${passCount}/${totalCount} 通过\n\n`;
        resultElement.textContent = results;
    }

    /**
     * 加载并运行解析器测试
     */
    async function loadAndRunParserTests(resultElement) {
        if (!resultElement) return;

        resultElement.textContent = '正在加载解析器测试...';

        try {
            // 动态加载测试脚本
            const script = document.createElement('script');
            script.src = './test-parser.js';
            script.onload = () => {
                if (typeof window.displayTestResults === 'function') {
                    window.displayTestResults();
                } else {
                    resultElement.textContent = '❌ 测试脚本加载失败';
                }
            };
            script.onerror = () => {
                resultElement.textContent = '❌ 无法加载测试脚本';
            };
            document.head.appendChild(script);

        } catch (error) {
            resultElement.textContent = `❌ 加载测试脚本时出错: ${error.message}`;
        }
    }

    /**
     * 回退到standalone版本
     */
    function fallbackToStandalone() {
        const loadingElement = document.getElementById('loading');
        if (loadingElement) {
            loadingElement.innerHTML = `
                <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
                <p>模块加载失败，正在回退到standalone版本...</p>
            `;
        }

        setTimeout(() => {
            window.location.href = '../../standalone.html';
        }, 2000);
    }

    /**
     * 检查浏览器兼容性
     */
    function checkBrowserCompatibility() {
        const features = {
            fetch: typeof fetch !== 'undefined',
            localStorage: typeof Storage !== 'undefined',
            fileAPI: typeof FileReader !== 'undefined',
            es6: typeof Symbol !== 'undefined'
        };
        
        const unsupported = Object.keys(features).filter(key => !features[key]);
        
        if (unsupported.length > 0) {
            log(`浏览器不支持以下特性: ${unsupported.join(', ')}`, 'error');
            return false;
        }
        
        return true;
    }

    /**
     * 初始化加载器
     */
    function init() {
        log('初始化模块加载器...');
        
        if (!checkBrowserCompatibility()) {
            fallbackToStandalone();
            return;
        }
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadAllModules);
        } else {
            loadAllModules();
        }
    }

    // 启动加载器
    init();

})();