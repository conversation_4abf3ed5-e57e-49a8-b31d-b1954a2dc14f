/* === Naming Conventions Header (auto-injected 2025-08-14) ===
    Performance monitor constants: PERF_MON_* if centralized later.
*/
/**
 * ==================== GoMyHire 对话分析系统 - 性能监控模块 ====================
 * 
 * @MODULE_INFO
 * 模块名称: Performance Monitor System
 * 版本: 2.0.0
 * 功能描述: 提供全面的性能监控功能，包括性能计时、内存监控、性能指标统计
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: utils.js (formatFileSize, safeExecute)
 * 间接依赖: 无
 * 被依赖: main.js, service-container.js, 以及需要性能监控的模块
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 1 (第二层，基础设施模块)
 * 加载时机: core (核心模块，立即加载)
 * 加载条件: 依赖utils.js加载完成
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - 性能计时器 (PerformanceTimer类)
 *   - 高级性能监控器 (AdvancedPerformanceMonitor类)
 *   - 内存使用监控
 *   - 操作计时和统计
 *   - 性能指标收集和分析
 * 导出接口: PerformanceTimer, AdvancedPerformanceMonitor, getPerformanceMonitor
 * 全局注册: window.ModuleExports['performance-monitor.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 操作名称、时间戳、性能配置
 * 输出数据: 性能指标、计时结果、监控报告
 * 状态管理: 维护计时器状态、性能数据缓存
 * 事件处理: 性能阈值报警事件
 * 
 * @INTEGRATION (集成关系)
 * UI集成: 为UI操作提供性能监控
 * 服务集成: 监控服务调用性能
 * 存储集成: 监控数据存储操作性能
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 低 (轻量级监控工具)
 * 加载性能: 快速 (基于原生性能API)
 * 运行时性能: 极小影响 (高效的计时机制)
 */

// 获取工具函数
function getUtils() {
    return window.ModuleExports && window.ModuleExports['utils.js'];
}

// ==================== 基础性能计时器类 ====================
/**
 * 性能计时器类 - 基础性能监控工具
 * @UTIL 性能监控工具
 */
class PerformanceTimer {
    constructor(name = 'Timer') {
        this.name = name;
        this.startTime = null;
        this.endTime = null;
        this.lapTimes = [];
    }

    /**
     * 开始计时
     * @SERVICE 计时开始方法
     * @returns {PerformanceTimer} 计时器实例
     */
    start() {
        this.startTime = performance.now();
        console.log(`[${this.name}] 开始计时`);
        return this;
    }

    /**
     * 结束计时
     * @SERVICE 计时结束方法
     * @returns {number} 执行时间（毫秒）
     */
    end() {
        this.endTime = performance.now();
        const duration = this.endTime - this.startTime;
        console.log(`[${this.name}] 执行时间: ${duration.toFixed(2)}ms`);
        return duration;
    }

    /**
     * 记录分段时间
     * @SERVICE 分段计时方法
     * @param {string} label - 分段标签
     * @returns {number} 分段时间（毫秒）
     */
    lap(label = 'Lap') {
        const currentTime = performance.now();
        const lapTime = currentTime - this.startTime;
        this.lapTimes.push({ label, time: lapTime, timestamp: currentTime });
        console.log(`[${this.name}] ${label}: ${lapTime.toFixed(2)}ms`);
        return lapTime;
    }

    /**
     * 获取所有分段时间
     * @SERVICE 分段时间获取方法
     * @returns {Array} 分段时间数组
     */
    getLapTimes() {
        return [...this.lapTimes];
    }

    /**
     * 重置计时器
     * @SERVICE 计时器重置方法
     */
    reset() {
        this.startTime = null;
        this.endTime = null;
        this.lapTimes = [];
    }
}

// ==================== 性能监控器类 ====================
/**
 * 性能监控器类 - 系统性能监控和指标收集
 * @COMPONENT 性能监控器
 */
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            fps: 0,
            memoryUsage: 0,
            cpuUsage: 0,
            networkLatency: 0,
            domNodes: 0
        };

        this.history = [];
        this.maxHistorySize = 60; // 保留60个数据点
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.frameCount = 0;
        this.lastFrameTime = performance.now();

        console.log('📊 PerformanceMonitor 初始化完成');
    }

    /**
     * 开始监控
     * @SERVICE 监控开始方法
     * @param {number} interval - 监控间隔（毫秒）
     */
    startMonitoring(interval = 1000) {
        if (this.isMonitoring) {
            console.warn('性能监控已在运行');
            return;
        }

        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics();
        }, interval);

        console.log('▶️ 性能监控已启动');
    }

    /**
     * 停止监控
     * @SERVICE 监控停止方法
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }

        console.log('⏹️ 性能监控已停止');
    }

    /**
     * 收集性能指标
     * @SERVICE 性能指标收集方法
     */
    collectMetrics() {
        // 收集FPS
        this.collectFPS();

        // 收集内存使用情况
        this.collectMemoryUsage();

        // 收集DOM节点数量
        this.collectDOMNodes();

        // 收集网络延迟（如果可用）
        this.collectNetworkLatency();

        // 记录历史
        this.recordHistory();
    }

    /**
     * 收集FPS数据
     * @UTIL FPS收集工具
     */
    collectFPS() {
        const now = performance.now();
        this.frameCount++;

        if (now - this.lastFrameTime >= 1000) {
            this.metrics.fps = Math.round((this.frameCount * 1000) / (now - this.lastFrameTime));
            this.frameCount = 0;
            this.lastFrameTime = now;
        }
    }

    /**
     * 收集内存使用情况
     * @UTIL 内存使用收集工具
     */
    collectMemoryUsage() {
        if (performance.memory) {
            this.metrics.memoryUsage = performance.memory.usedJSHeapSize;
        } else {
            this.metrics.memoryUsage = 0;
        }
    }

    /**
     * 收集DOM节点数量
     * @UTIL DOM节点收集工具
     */
    collectDOMNodes() {
        this.metrics.domNodes = document.querySelectorAll('*').length;
    }

    /**
     * 收集网络延迟
     * @UTIL 网络延迟收集工具
     */
    collectNetworkLatency() {
        // 简单的网络延迟检测
        const start = performance.now();
        fetch('data:text/plain,ping', { method: 'HEAD' })
            .then(() => {
                this.metrics.networkLatency = performance.now() - start;
            })
            .catch(() => {
                this.metrics.networkLatency = -1; // 表示无法测量
            });
    }

    /**
     * 记录历史数据
     * @SERVICE 历史数据记录方法
     */
    recordHistory() {
        const historyItem = {
            ...this.metrics,
            timestamp: Date.now()
        };

        this.history.push(historyItem);

        // 限制历史大小
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
        }
    }

    /**
     * 获取系统负载
     * @SERVICE 系统负载获取方法
     * @returns {Object} 系统负载信息
     */
    getSystemLoad() {
        const avgFPS = this.history.length > 0
            ? this.history.reduce((sum, h) => sum + h.fps, 0) / this.history.length
            : 0;

        const avgMemory = this.history.length > 0
            ? this.history.reduce((sum, h) => sum + h.memoryUsage, 0) / this.history.length
            : 0;

        let loadLevel = 'low';
        if (avgFPS < 30 || avgMemory > 100 * 1024 * 1024) {
            loadLevel = 'high';
        } else if (avgFPS < 45 || avgMemory > 50 * 1024 * 1024) {
            loadLevel = 'medium';
        }

        return {
            level: loadLevel,
            fps: Math.round(avgFPS),
            memory: avgMemory,
            formattedMemory: getUtils()?.formatFileSize(avgMemory) || avgMemory + ' bytes'
        };
    }

    /**
     * 获取性能报告
     * @SERVICE 性能报告获取方法
     * @returns {Object} 性能报告
     */
    getPerformanceReport() {
        const systemLoad = this.getSystemLoad();

        return {
            current: { ...this.metrics },
            systemLoad,
            history: [...this.history],
            isMonitoring: this.isMonitoring,
            formattedMetrics: {
                memoryUsage: getUtils()?.formatFileSize(this.metrics.memoryUsage) || this.metrics.memoryUsage + ' bytes',
                fps: `${this.metrics.fps} FPS`,
                domNodes: `${this.metrics.domNodes} 节点`,
                networkLatency: this.metrics.networkLatency >= 0 
                    ? `${this.metrics.networkLatency.toFixed(2)}ms` 
                    : '无法测量'
            }
        };
    }

    /**
     * 检查性能警告
     * @SERVICE 性能警告检查方法
     * @returns {Array} 警告列表
     */
    checkPerformanceWarnings() {
        const warnings = [];

        if (this.metrics.fps < 30) {
            warnings.push({
                type: 'fps',
                message: 'FPS过低，可能影响用户体验',
                value: this.metrics.fps,
                threshold: 30
            });
        }

        if (this.metrics.memoryUsage > 100 * 1024 * 1024) { // 100MB
            warnings.push({
                type: 'memory',
                message: '内存使用过高',
                value: this.metrics.memoryUsage,
                threshold: 100 * 1024 * 1024,
                formatted: getUtils()?.formatFileSize(this.metrics.memoryUsage) || this.metrics.memoryUsage + ' bytes'
            });
        }

        if (this.metrics.domNodes > 5000) {
            warnings.push({
                type: 'dom',
                message: 'DOM节点过多，可能影响性能',
                value: this.metrics.domNodes,
                threshold: 5000
            });
        }

        if (this.metrics.networkLatency > 1000) {
            warnings.push({
                type: 'network',
                message: '网络延迟过高',
                value: this.metrics.networkLatency,
                threshold: 1000
            });
        }

        return warnings;
    }

    /**
     * 记录渲染时间
     * @SERVICE 渲染时间记录方法
     * @param {number} startTime - 开始时间
     * @param {number} endTime - 结束时间
     */
    recordRenderTime(startTime, endTime) {
        const renderTime = endTime - startTime;
        console.log(`🎨 渲染时间: ${renderTime.toFixed(2)}ms`);
        
        // 可以在这里记录到历史数据中
        if (!this.renderHistory) {
            this.renderHistory = [];
        }
        
        this.renderHistory.push({
            renderTime,
            timestamp: Date.now()
        });

        // 限制历史大小
        if (this.renderHistory.length > 100) {
            this.renderHistory.shift();
        }
    }

    /**
     * 记录更新时间
     * @SERVICE 更新时间记录方法
     * @param {number} startTime - 开始时间
     * @param {number} endTime - 结束时间
     */
    recordUpdateTime(startTime, endTime) {
        const updateTime = endTime - startTime;
        console.log(`🔄 更新时间: ${updateTime.toFixed(2)}ms`);
        
        // 可以在这里记录到历史数据中
        if (!this.updateHistory) {
            this.updateHistory = [];
        }
        
        this.updateHistory.push({
            updateTime,
            timestamp: Date.now()
        });

        // 限制历史大小
        if (this.updateHistory.length > 100) {
            this.updateHistory.shift();
        }
    }

    /**
     * 获取渲染统计
     * @SERVICE 渲染统计获取方法
     * @returns {Object} 渲染统计信息
     */
    getRenderStats() {
        if (!this.renderHistory || this.renderHistory.length === 0) {
            return { count: 0, averageTime: 0, totalTime: 0 };
        }

        const totalTime = this.renderHistory.reduce((sum, item) => sum + item.renderTime, 0);
        const averageTime = totalTime / this.renderHistory.length;

        return {
            count: this.renderHistory.length,
            averageTime: averageTime.toFixed(2),
            totalTime: totalTime.toFixed(2),
            minTime: Math.min(...this.renderHistory.map(item => item.renderTime)).toFixed(2),
            maxTime: Math.max(...this.renderHistory.map(item => item.renderTime)).toFixed(2)
        };
    }

    /**
     * 销毁监控器
     * @LIFECYCLE 监控器销毁方法
     */
    destroy() {
        this.stopMonitoring();
        this.history = [];
        this.renderHistory = [];
        this.updateHistory = [];
        console.log('🗑️ PerformanceMonitor 已销毁');
    }
}

// ==================== UI性能监控器类 ====================
/**
 * UI性能监控器类 - 专门监控UI性能指标和渲染性能
 * @COMPONENT UI性能监控器
 */
class UIPerformanceMonitor {
    constructor() {
        this.metrics = {
            fps: 0,
            frameTime: 0,
            renderTime: 0,
            updateTime: 0,
            memoryUsage: 0,
            domNodes: 0
        };

        this.history = [];
        this.maxHistorySize = 100;
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.frameCount = 0;
        this.lastFrameTime = performance.now();

        console.log('🖥️ UIPerformanceMonitor 初始化完成');
    }

    /**
     * 开始UI性能监控
     * @SERVICE UI监控开始方法
     * @param {number} interval - 监控间隔（毫秒）
     */
    startMonitoring(interval = 1000) {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics();
        }, interval);

        console.log('▶️ UI性能监控已启动');
    }

    /**
     * 停止UI性能监控
     * @SERVICE UI监控停止方法
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }

        console.log('⏹️ UI性能监控已停止');
    }

    /**
     * 收集UI性能指标
     * @SERVICE UI性能指标收集方法
     */
    collectMetrics() {
        const now = performance.now();

        // 计算FPS
        this.calculateFPS();

        // 获取内存使用情况
        if (performance.memory) {
            this.metrics.memoryUsage = performance.memory.usedJSHeapSize;
        }

        // 获取DOM节点数量
        this.metrics.domNodes = document.querySelectorAll('*').length;

        // 记录历史
        this.history.push({
            timestamp: now,
            ...this.metrics
        });

        // 限制历史大小
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
        }
    }

    /**
     * 计算FPS
     * @UTIL FPS计算工具
     */
    calculateFPS() {
        const now = performance.now();
        this.frameCount++;

        if (now - this.lastFrameTime >= 1000) {
            this.metrics.fps = Math.round((this.frameCount * 1000) / (now - this.lastFrameTime));
            this.metrics.frameTime = (now - this.lastFrameTime) / this.frameCount;
            this.frameCount = 0;
            this.lastFrameTime = now;
        }
    }

    /**
     * 记录渲染时间
     * @SERVICE 渲染时间记录方法
     * @param {number} startTime - 开始时间
     * @param {number} endTime - 结束时间
     */
    recordRenderTime(startTime, endTime) {
        this.metrics.renderTime = endTime - startTime;
    }

    /**
     * 记录更新时间
     * @SERVICE 更新时间记录方法
     * @param {number} startTime - 开始时间
     * @param {number} endTime - 结束时间
     */
    recordUpdateTime(startTime, endTime) {
        this.metrics.updateTime = endTime - startTime;
    }

    /**
     * 获取UI性能报告
     * @SERVICE UI性能报告获取方法
     * @returns {Object} UI性能报告
     */
    getPerformanceReport() {
        const avgFPS = this.history.length > 0
            ? this.history.reduce((sum, h) => sum + h.fps, 0) / this.history.length
            : 0;

        const avgMemory = this.history.length > 0
            ? this.history.reduce((sum, h) => sum + h.memoryUsage, 0) / this.history.length
            : 0;

        return {
            current: { ...this.metrics },
            averages: {
                fps: Math.round(avgFPS),
                memoryUsage: Math.round(avgMemory),
                formattedMemory: getUtils()?.formatFileSize(avgMemory) || avgMemory + ' bytes'
            },
            history: [...this.history]
        };
    }

    /**
     * 检查UI性能警告
     * @SERVICE UI性能警告检查方法
     * @returns {Array} 警告列表
     */
    checkPerformanceWarnings() {
        const warnings = [];

        if (this.metrics.fps < 30) {
            warnings.push('FPS过低，可能影响用户体验');
        }

        if (this.metrics.memoryUsage > 100 * 1024 * 1024) { // 100MB
            warnings.push('内存使用过高');
        }

        if (this.metrics.domNodes > 5000) {
            warnings.push('DOM节点过多，可能影响性能');
        }

        if (this.metrics.renderTime > 16.67) { // 60FPS阈值
            warnings.push('渲染时间过长，可能导致掉帧');
        }

        return warnings;
    }

    /**
     * 销毁UI性能监控器
     * @LIFECYCLE UI监控器销毁方法
     */
    destroy() {
        this.stopMonitoring();
        this.history = [];
        console.log('🗑️ UIPerformanceMonitor 已销毁');
    }
}

// ==================== 图表性能监控器类 ====================
/**
 * 图表性能监控器类 - 专门监控图表渲染性能和系统资源使用情况
 * @COMPONENT 图表性能监控器
 */
class ChartPerformanceMonitor {
    constructor() {
        this.performanceMetrics = new Map();
        this.resourceUsage = new Map();
        this.alertThresholds = {
            renderTime: 1000,      // 1秒
            memoryUsage: 0.8,      // 80%
            cpuUsage: 0.9          // 90%
        };
        this.isMonitoring = false;
        this.monitoringInterval = null;

        console.log('📊 ChartPerformanceMonitor 初始化完成');
    }

    /**
     * 开始图表性能监控
     * @SERVICE 图表监控开始方法
     * @param {number} interval - 监控间隔（毫秒）
     */
    startMonitoring(interval = 2000) {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.collectPerformanceData();
        }, interval);

        console.log('▶️ 图表性能监控已启动');
    }

    /**
     * 停止图表性能监控
     * @SERVICE 图表监控停止方法
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }

        console.log('⏹️ 图表性能监控已停止');
    }

    /**
     * 记录图表渲染性能
     * @SERVICE 图表渲染性能记录方法
     * @param {string} chartId - 图表ID
     * @param {number} renderTime - 渲染时间
     * @param {string} strategy - 渲染策略
     */
    recordRender(chartId, renderTime, strategy) {
        const timestamp = Date.now();

        if (!this.performanceMetrics.has(chartId)) {
            this.performanceMetrics.set(chartId, []);
        }

        const metrics = this.performanceMetrics.get(chartId);
        metrics.push({
            timestamp,
            renderTime,
            strategy,
            memoryUsage: this.getMemoryUsage(),
            cpuUsage: this.getCPUUsage()
        });

        // 限制历史记录
        if (metrics.length > 50) {
            metrics.shift();
        }

        this.checkPerformanceAlerts(chartId, renderTime);
    }

    /**
     * 收集图表性能数据
     * @SERVICE 图表性能数据收集方法
     */
    collectPerformanceData() {
        const timestamp = Date.now();
        const memoryUsage = this.getMemoryUsage();
        const cpuUsage = this.getCPUUsage();

        this.resourceUsage.set(timestamp, {
            memory: memoryUsage,
            cpu: cpuUsage,
            timestamp
        });

        // 限制资源使用历史记录
        const entries = Array.from(this.resourceUsage.entries());
        if (entries.length > 200) {
            const oldestKey = entries[0][0];
            this.resourceUsage.delete(oldestKey);
        }

        this.checkResourceAlerts(memoryUsage, cpuUsage);
    }

    /**
     * 获取内存使用情况
     * @UTIL 内存使用获取工具
     * @returns {number} 内存使用率（0-1）
     */
    getMemoryUsage() {
        if (performance.memory) {
            const used = performance.memory.usedJSHeapSize;
            const total = performance.memory.totalJSHeapSize;
            return used / total;
        }
        return 0;
    }

    /**
     * 获取CPU使用情况（模拟）
     * @UTIL CPU使用获取工具
     * @returns {number} CPU使用率（0-1）
     */
    getCPUUsage() {
        // 浏览器中无法直接获取CPU使用率，这里使用模拟值
        // 实际应用中可以通过渲染时间等指标来估算
        return Math.random() * 0.3; // 模拟0-30%的CPU使用率
    }

    /**
     * 检查图表性能警告
     * @SERVICE 图表性能警告检查方法
     * @param {string} chartId - 图表ID
     * @param {number} renderTime - 渲染时间
     */
    checkPerformanceAlerts(chartId, renderTime) {
        if (renderTime > this.alertThresholds.renderTime) {
            console.warn(`⚠️ 图表渲染时间过长: ${chartId} (${renderTime}ms)`);
        }
    }

    /**
     * 检查资源使用警告
     * @SERVICE 资源使用警告检查方法
     * @param {number} memoryUsage - 内存使用率
     * @param {number} cpuUsage - CPU使用率
     */
    checkResourceAlerts(memoryUsage, cpuUsage) {
        if (memoryUsage > this.alertThresholds.memoryUsage) {
            console.warn(`⚠️ 内存使用率过高: ${(memoryUsage * 100).toFixed(1)}%`);
        }

        if (cpuUsage > this.alertThresholds.cpuUsage) {
            console.warn(`⚠️ CPU使用率过高: ${(cpuUsage * 100).toFixed(1)}%`);
        }
    }

    /**
     * 获取图表性能统计
     * @SERVICE 图表性能统计获取方法
     * @returns {Object} 性能统计信息
     */
    getPerformanceStats() {
        const stats = {
            totalCharts: this.performanceMetrics.size,
            averageRenderTime: 0,
            slowestChart: null,
            fastestChart: null,
            memoryTrend: [],
            cpuTrend: [],
            alertCount: 0
        };

        // 计算平均渲染时间
        let totalRenderTime = 0;
        let totalRenders = 0;
        let slowestTime = 0;
        let fastestTime = Infinity;

        for (const [chartId, metrics] of this.performanceMetrics) {
            for (const metric of metrics) {
                totalRenderTime += metric.renderTime;
                totalRenders++;

                if (metric.renderTime > slowestTime) {
                    slowestTime = metric.renderTime;
                    stats.slowestChart = { chartId, renderTime: metric.renderTime };
                }

                if (metric.renderTime < fastestTime) {
                    fastestTime = metric.renderTime;
                    stats.fastestChart = { chartId, renderTime: metric.renderTime };
                }

                if (metric.renderTime > this.alertThresholds.renderTime) {
                    stats.alertCount++;
                }
            }
        }

        if (totalRenders > 0) {
            stats.averageRenderTime = totalRenderTime / totalRenders;
        }

        // 获取资源使用趋势
        const resourceEntries = Array.from(this.resourceUsage.values()).slice(-20);
        stats.memoryTrend = resourceEntries.map(entry => entry.memory);
        stats.cpuTrend = resourceEntries.map(entry => entry.cpu);

        return stats;
    }

    /**
     * 获取图表性能详情
     * @SERVICE 图表性能详情获取方法
     * @param {string} chartId - 图表ID
     * @returns {Object|null} 图表性能详情
     */
    getChartPerformance(chartId) {
        const metrics = this.performanceMetrics.get(chartId);
        if (!metrics || metrics.length === 0) return null;

        const renderTimes = metrics.map(m => m.renderTime);
        const strategies = metrics.map(m => m.strategy);

        return {
            chartId,
            totalRenders: metrics.length,
            averageRenderTime: renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length,
            minRenderTime: Math.min(...renderTimes),
            maxRenderTime: Math.max(...renderTimes),
            mostUsedStrategy: this.getMostFrequent(strategies),
            recentMetrics: metrics.slice(-10)
        };
    }

    /**
     * 获取最频繁的值
     * @UTIL 最频繁值获取工具
     * @param {Array} array - 数组
     * @returns {any} 最频繁的值
     */
    getMostFrequent(array) {
        const frequency = {};
        let maxCount = 0;
        let mostFrequent = null;

        for (const item of array) {
            frequency[item] = (frequency[item] || 0) + 1;
            if (frequency[item] > maxCount) {
                maxCount = frequency[item];
                mostFrequent = item;
            }
        }

        return mostFrequent;
    }

    /**
     * 销毁图表性能监控器
     * @LIFECYCLE 图表监控器销毁方法
     */
    destroy() {
        this.stopMonitoring();
        this.performanceMetrics.clear();
        this.resourceUsage.clear();
        console.log('🗑️ ChartPerformanceMonitor 已销毁');
    }
}

// ==================== 全局性能监控实例 ====================
let performanceMonitorInstance = null;

/**
 * 获取全局性能监控实例
 * @SERVICE 全局性能监控获取函数
 */
function getPerformanceMonitor() {
    if (!performanceMonitorInstance) {
        performanceMonitorInstance = new PerformanceMonitor();
    }
    return performanceMonitorInstance;
}

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['performance-monitor.js'] = {
    PerformanceTimer,
    PerformanceMonitor,
    UIPerformanceMonitor,
    ChartPerformanceMonitor,
    getPerformanceMonitor
};
