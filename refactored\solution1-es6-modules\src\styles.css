/* ==================== GoMyHire 对话分析系统 - 完整样式系统 ==================== */
/* @SHARED_STYLE 从standalone.html完整迁移的CSS样式系统 */

/* ==================== CSS变量定义 ==================== */
/* @SHARED_STYLE 全局CSS变量 - 统一管理颜色、间距、阴影等设计令牌 */
:root {
    /* 主色调定义 */
    --primary: #3b82f6;          /* 主要品牌色 */
    --primary-dark: #2563eb;     /* 主色调深色版本 */
    --primary-500: #3b82f6;      /* 主色调 500 */
    --secondary: #64748b;        /* 次要色调 */
    --success: #10b981;          /* 成功状态色 */
    --warning: #f59e0b;          /* 警告状态色 */
    --danger: #ef4444;           /* 危险状态色 */
    --info: #06b6d4;             /* 信息提示色 */

    /* 扩展颜色系统 */
    --green-500: #10b981;        /* 绿色 */
    --blue-500: #3b82f6;         /* 蓝色 */
    --yellow-500: #f59e0b;       /* 黄色 */
    --red-500: #ef4444;          /* 红色 */
    --purple-500: #8b5cf6;       /* 紫色 */

    /* 中性色调定义 */
    --white: #ffffff;            /* 纯白色 */
    --gray-50: #f9fafb;          /* 最浅灰色 - 背景色 */
    --gray-100: #f3f4f6;         /* 浅灰色 - 卡片背景 */
    --gray-200: #e5e7eb;         /* 边框色 */
    --gray-300: #d1d5db;         /* 分割线色 */
    --gray-400: #9ca3af;         /* 占位符文字色 */
    --gray-500: #6b7280;         /* 次要文字色 */
    --gray-600: #4b5563;         /* 标签文字色 */
    --gray-700: #374151;         /* 主要文字色 */
    --gray-800: #1f2937;         /* 标题文字色 */
    --gray-900: #111827;         /* 最深文字色 */

    /* 间距系统定义 */
    --spacing-xs: 0.25rem;       /* 4px - 最小间距 */
    --spacing-sm: 0.5rem;        /* 8px - 小间距 */
    --spacing-md: 1rem;          /* 16px - 标准间距 */
    --spacing-lg: 1.5rem;        /* 24px - 大间距 */
    --spacing-xl: 2rem;          /* 32px - 超大间距 */
    --spacing-2xl: 3rem;         /* 48px - 最大间距 */

    /* 圆角系统定义 */
    --radius-sm: 0.25rem;        /* 4px - 小圆角 */
    --radius-md: 0.5rem;         /* 8px - 标准圆角 */
    --radius-lg: 0.75rem;        /* 12px - 大圆角 */

    /* 阴影系统定义 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);                                    /* 轻微阴影 */
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* 标准阴影 */
}

/* ==================== 基础样式重置 ==================== */
/* CSS重置 - 统一浏览器默认样式差异 */
* {
    margin: 0;                   /* 清除默认外边距 */
    padding: 0;                  /* 清除默认内边距 */
    box-sizing: border-box;      /* 使用边框盒模型 */
}

/* 页面基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; /* 系统字体栈 */
    line-height: 1.6;            /* 行高设置 */
    color: var(--gray-700);      /* 默认文字颜色 */
    background: var(--gray-50);  /* 页面背景色 */
}

/* ==================== 布局容器样式 ==================== */
/* @LAYOUT_COMPONENT 主容器 - 页面内容的最大宽度和居中对齐 */
.container {
    max-width: 1200px;           /* 最大宽度限制 */
    margin: 0 auto;              /* 水平居中 */
    padding: 0 var(--spacing-lg); /* 左右内边距 */
}

/* @LAYOUT_COMPONENT 主网格布局 - 页面主要内容区域的网格系统 */
.main-grid {
    display: grid;                       /* 网格布局 */
    grid-template-columns: 1fr;          /* 单列布局 */
    gap: var(--spacing-xl);              /* 网格间距 */
    margin-bottom: var(--spacing-2xl);   /* 底部外边距 */
}

/* 头部样式 */
.header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) 0;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 标签页导航 */
.tabs-container {
    margin-bottom: var(--spacing-xl);
}

.tabs-nav {
    display: flex;
    border-bottom: 1px solid var(--gray-200);
    background: var(--white);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.tab-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--gray-600);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
}

.tab-btn:hover {
    color: var(--primary);
    background: var(--gray-50);
}

.tab-btn.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
    background: var(--white);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 卡片样式 */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-body {
    padding: var(--spacing-lg);
}

/* 按钮样式 */
.btn-primary {
    background: var(--primary);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-secondary:hover {
    border-color: var(--primary);
    color: var(--primary);
}

.btn-outline-danger {
    background: transparent;
    color: var(--danger);
    border: 1px solid var(--danger);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-outline-danger:hover {
    background: var(--danger);
    color: var(--white);
}

/* 文件上传区域 */
.drop-zone {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.drop-zone:hover {
    border-color: var(--primary);
    background: var(--gray-50);
}

.drop-zone.dragover {
    border-color: var(--primary);
    background: var(--primary);
    background-opacity: 0.1;
}

.drop-zone i {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: var(--spacing-md);
}

.drop-zone p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-md);
}

/* 统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--radius-md);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: var(--primary);
    color: var(--white);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* ==================== 文件上传组件样式 ==================== */
/* 紧凑上传区域 - 扁平化设计风格 */
.upload-section-compact {
    background: var(--white);            /* 白色背景 */
    border: 1px solid var(--gray-200);   /* 边框颜色 */
    border-radius: var(--radius-sm);     /* 小圆角 */
    padding: var(--spacing-md);          /* 内边距 */
    margin-bottom: var(--spacing-lg);    /* 底部外边距 */
}

/* 上传内容区域 - 简化信息层次 */
.upload-zone {
    display: flex;                       /* 弹性布局 */
    flex-direction: column;              /* 垂直排列 */
    align-items: center;                 /* 水平居中 */
    gap: var(--spacing-sm);              /* 元素间距 */
}

/* 上传区域主图标 */
.upload-icon-main {
    font-size: 24px;                    /* 图标大小 */
    color: var(--gray-500);              /* 图标颜色 */
}

/* 上传区域主文字 */
.upload-text-main {
    font-size: 14px;                    /* 字体大小 */
    color: var(--gray-700);              /* 文字颜色 */
    font-weight: 500;                    /* 字体粗细 */
    margin: 0;                           /* 清除外边距 */
}

/* 上传区域提示文字 */
.upload-hint-main {
    font-size: 12px;                    /* 字体大小 */
    color: var(--gray-500);              /* 文字颜色 */
    margin: 0;                           /* 清除外边距 */
}

/* 快速操作栏 - 水平排列的操作按钮 */
.upload-actions {
    display: flex;                       /* 弹性布局 */
    gap: var(--spacing-sm);              /* 按钮间距 */
    justify-content: center;             /* 水平居中 */
    margin-bottom: var(--spacing-md);    /* 底部外边距 */
}

/* ==================== 按钮组件样式 ==================== */
/* 上传操作按钮 - 垂直布局的图标按钮 */
.upload-btn {
    display: flex;                       /* 弹性布局 */
    flex-direction: column;              /* 垂直排列 */
    align-items: center;                 /* 水平居中 */
    gap: var(--spacing-xs);              /* 图标与文字间距 */
    padding: 12px 16px;                  /* 内边距 */
    border: 1px solid var(--gray-200);   /* 边框 */
    border-radius: var(--radius-sm);     /* 小圆角 */
    background: var(--white);            /* 白色背景 */
    color: var(--gray-700);              /* 文字颜色 */
    font-size: 12px;                     /* 字体大小 */
    cursor: pointer;                     /* 鼠标指针 */
    transition: all 0.2s ease;           /* 过渡动画 */
    min-width: 80px;                     /* 最小宽度 */
}

/* 上传按钮悬停状态 */
.upload-btn:hover {
    border-color: var(--primary);        /* 主色调边框 */
    color: var(--primary);               /* 主色调文字 */
}

/* 上传按钮激活状态 */
.upload-btn.active {
    border-color: var(--primary);        /* 主色调边框 */
    background: #eff6ff;                 /* 浅蓝背景 */
    color: var(--primary);               /* 主色调文字 */
}

/* 上传按钮图标样式 */
.upload-btn i {
    font-size: 16px;                    /* 图标大小 */
}

/* 上传按钮文字样式 */
.upload-btn span {
    font-weight: 500;                    /* 字体粗细 */
}

/* 图表网格 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.chart-container {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    position: relative;
    width: 100%;
    height: 300px;
}

.chart {
    width: 100%;
    height: 300px;
    background: var(--gray-50);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    margin-bottom: var(--spacing-sm);
}

.chart-container h4 {
    text-align: center;
    color: var(--gray-700);
    font-weight: 600;
}

.chart-container-small {
    height: 250px;
    min-height: 200px;
}

.chart-container .text-muted {
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
}

/* ==================== 进度显示组件样式 ==================== */
/* 进度条容器 */
.progress-container {
    margin-bottom: var(--spacing-lg);    /* 底部外边距 */
}

/* 主进度条容器 */
.progress-bar {
    width: 100%;                         /* 全宽 */
    height: 20px;                        /* 高度 */
    background: var(--gray-200);         /* 背景色 */
    border-radius: 10px;                 /* 圆角 */
    overflow: hidden;                    /* 隐藏溢出 */
    margin-bottom: var(--spacing-sm);    /* 底部外边距 */
}

/* 进度条填充 */
.progress-fill {
    height: 100%;                        /* 全高 */
    background: linear-gradient(90deg, var(--primary), var(--primary-dark)); /* 渐变背景 */
    width: 0%;                           /* 初始宽度 */
    transition: width 0.3s ease;         /* 宽度过渡动画 */
}

/* 进度信息显示 */
.progress-info {
    display: flex;                       /* 弹性布局 */
    justify-content: space-between;      /* 两端对齐 */
    font-size: 0.875rem;                 /* 字体大小 */
    color: var(--gray-600);              /* 文字颜色 */
}

/* 进度文字显示 */
.progress-text {
    font-size: 0.875rem;                /* 字体大小 */
    color: var(--gray-600);             /* 文字颜色 */
    display: flex;                      /* 弹性布局 */
    justify-content: space-between;     /* 两端对齐 */
}

/* 上传进度条容器 */
.upload-progress-bar {
    width: 100%;                         /* 全宽 */
    height: 8px;                         /* 高度 */
    background: var(--gray-100);         /* 背景色 */
    border-radius: var(--radius-sm);     /* 小圆角 */
    overflow: hidden;                    /* 隐藏溢出 */
    margin-bottom: var(--spacing-sm);    /* 底部外边距 */
}

/* 上传进度条填充 */
.upload-progress-fill {
    height: 100%;                        /* 全高 */
    background: var(--primary);          /* 主色调背景 */
    transition: width 0.3s ease;         /* 宽度过渡动画 */
}

/* 上传进度文字 */
.upload-progress-text {
    font-size: 12px;                     /* 字体大小 */
    color: var(--gray-500);              /* 文字颜色 */
    text-align: center;                  /* 文字居中 */
}

/* ==================== 模态框系统样式 ==================== */
/* 模态框遮罩层 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

/* 模态框内容容器 */
.modal-content {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 模态框头部 */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
}

/* 模态框关闭按钮 */
.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--gray-400);
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: var(--gray-600);
}

/* 模态框主体 */
.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    flex: 1;
}

/* 模态框底部 */
.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* 分析控制 */
.analysis-controls {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

/* 设置网格 */
.settings-grid {
    display: grid;
    gap: var(--spacing-lg);
    max-width: 600px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.setting-item label {
    font-weight: 600;
    color: var(--gray-700);
}

.form-control {
    padding: var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 进度显示 */
.upload-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    min-width: 300px;
    z-index: 1000;
}

.upload-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.upload-status-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
}

.upload-cancel-btn {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-xs);
}

.upload-progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.upload-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--primary-dark));
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.upload-progress-text {
    font-size: 0.875rem;
    color: var(--gray-600);
    text-align: center;
}

/* 通知消息 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    font-weight: 500;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
}

.message-toast.message-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.message-toast.message-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.message-toast.message-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.message-toast.message-info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

.message-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.message-close:hover {
    opacity: 1;
}

/* 工具类 */
.hidden {
    display: none !important;
}

.loading {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--gray-600);
}

.loading i {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ==================== 通知系统样式 ==================== */
/* 通知容器 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-width: 400px;
}

/* 通知基础样式 */
.notification {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    animation: slideInRight 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* 成功通知 */
.notification.success {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
}

/* 警告通知 */
.notification.warning {
    background: #fffbeb;
    border: 1px solid #fed7aa;
    color: #d97706;
}

/* 错误通知 */
.notification.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

/* 信息通知 */
.notification.info {
    background: #eff6ff;
    border: 1px solid #bfdbfe;
    color: #2563eb;
}

/* 通知图标 */
.notification-icon {
    font-size: 18px;
    flex-shrink: 0;
}

/* 通知内容 */
.notification-content {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
}

/* 通知关闭按钮 */
.notification-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.2s ease;
    flex-shrink: 0;
}

.notification-close:hover {
    opacity: 1;
}

/* ==================== 标签页系统样式 ==================== */
/* 标签页导航容器 */
.tab-navigation {
    display: flex;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    overflow: hidden;
}

/* 标签页按钮 */
.tab-btn {
    flex: 1;                            /* 等分空间 */
    padding: var(--spacing-md) var(--spacing-lg); /* 内边距 */
    background: var(--gray-50);         /* 浅灰背景 */
    border: none;                       /* 无边框 */
    border-right: 1px solid var(--gray-200); /* 右边框 */
    color: var(--gray-600);             /* 文字颜色 */
    font-weight: 500;                   /* 字体粗细 */
    cursor: pointer;                    /* 鼠标指针 */
    transition: all 0.2s ease;          /* 过渡动画 */
    display: flex;                      /* 弹性布局 */
    align-items: center;                /* 垂直居中 */
    justify-content: center;            /* 水平居中 */
    gap: var(--spacing-xs);             /* 图标与文字间距 */
}

/* 最后一个标签页按钮 */
.tab-btn:last-child {
    border-right: none;                 /* 移除右边框 */
}

/* 标签页按钮悬停状态 */
.tab-btn:hover {
    background: var(--gray-100);        /* 更深的灰色背景 */
    color: var(--gray-800);             /* 深色文字 */
}

/* 标签页按钮激活状态 */
.tab-btn.active {
    background: var(--primary);         /* 主色调背景 */
    color: var(--white);                /* 白色文字 */
}

/* 标签页内容容器 */
.tab-content {
    display: none;                      /* 默认隐藏 */
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-top: none;
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    padding: var(--spacing-lg);
}

/* 激活的标签页内容 */
.tab-content.active {
    display: block;                     /* 显示激活内容 */
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .tabs-nav {
        flex-direction: column;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .analysis-controls {
        flex-direction: column;
        align-items: stretch;
    }

    /* 通知系统移动端适配 */
    .notification-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
        max-width: none;
    }

    .notification {
        padding: var(--spacing-sm);
    }

    /* 标签页按钮移动端适配 */
    .tab-navigation {
        flex-direction: column;
    }

    .tab-btn {
        border-right: none;             /* 移除右边框 */
        border-bottom: 1px solid var(--gray-200); /* 底部边框 */
    }

    /* 最后一个标签页按钮移动端适配 */
    .tab-btn:last-child {
        border-bottom: none;            /* 移除底部边框 */
    }
}
