/* === Naming Conventions Header (auto-injected 2025-08-14) ===
    Functional tester constants: FUNC_TEST_* prefix.
*/
/**
 * ==================== GoMyHire 对话分析系统 - 功能验证测试器 ====================
 * @SERVICE 完整功能验证测试工具
 * 实现所有原有功能的验证测试，确保新架构中功能完整性
 */

import { generateUniqueId, safeExecute } from './utils.js';
import { getMainApp } from './main.js';

// ==================== 功能验证测试器类 ====================
/**
 * 功能验证测试器类 - 负责完整功能验证测试
 * @COMPONENT 功能验证测试器
 */
export class FunctionalTester {
    constructor() {
        this.mainApp = null;
        this.testResults = new Map();
        this.isRunning = false;
        this.testSuites = new Map();

        this.initialize();
        console.log('🧪 FunctionalTester 初始化完成');
    }

    /**
     * 初始化功能测试器
     * @INIT 功能测试器初始化方法
     */
    initialize() {
        this.mainApp = getMainApp();
        this.setupTestSuites();
    }

    /**
     * 设置测试套件
     * @SERVICE 测试套件设置方法
     */
    setupTestSuites() {
        // 模块加载测试
        this.testSuites.set('moduleLoading', {
            name: '模块加载测试',
            description: '验证所有模块正确加载',
            tests: [
                'testCoreModulesLoaded',
                'testUIComponentsLoaded',
                'testAdvancedFeaturesLoaded'
            ]
        });

        // 文件上传测试
        this.testSuites.set('fileUpload', {
            name: '文件上传测试',
            description: '验证文件上传和处理功能',
            tests: [
                'testFileSelection',
                'testDragAndDrop',
                'testFileValidation',
                'testBatchUpload'
            ]
        });

        // 数据处理测试
        this.testSuites.set('dataProcessing', {
            name: '数据处理测试',
            description: '验证数据解析和处理功能',
            tests: [
                'testTextParsing',
                'testDataStorage',
                'testDataExport'
            ]
        });

        // UI组件测试
        this.testSuites.set('uiComponents', {
            name: 'UI组件测试',
            description: '验证UI组件功能',
            tests: [
                'testNotificationSystem',
                'testModalSystem',
                'testTabSystem',
                'testProgressSystem'
            ]
        });

        // 报告生成测试
        this.testSuites.set('reportGeneration', {
            name: '报告生成测试',
            description: '验证报告生成功能',
            tests: [
                'testReportCreation',
                'testReportExport',
                'testReportHistory'
            ]
        });

        console.log(`📋 设置了 ${this.testSuites.size} 个测试套件`);
    }

    /**
     * 运行完整功能验证测试
     * @SERVICE 完整功能验证测试运行方法
     * @returns {Promise<Object>} 测试结果
     */
    async runFullFunctionalTest() {
        if (this.isRunning) {
            console.warn('功能测试正在运行中');
            return null;
        }

        this.isRunning = true;
        const startTime = performance.now();

        try {
            console.log('🚀 开始完整功能验证测试...');

            const results = {};
            let totalTests = 0;
            let passedTests = 0;
            let failedTests = 0;

            // 运行所有测试套件
            for (const [suiteName, suite] of this.testSuites) {
                console.log(`📦 运行测试套件: ${suite.name}`);
                
                const suiteResult = await this.runTestSuite(suiteName);
                results[suiteName] = suiteResult;
                
                totalTests += suiteResult.totalTests;
                passedTests += suiteResult.passedTests;
                failedTests += suiteResult.failedTests;
            }

            const endTime = performance.now();
            const totalTime = endTime - startTime;

            const overallResult = {
                success: failedTests === 0,
                totalTests: totalTests,
                passedTests: passedTests,
                failedTests: failedTests,
                passRate: totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0,
                totalTime: totalTime,
                timestamp: new Date().toISOString(),
                suiteResults: results
            };

            console.log(`✅ 功能验证测试完成: ${passedTests}/${totalTests} 通过 (${overallResult.passRate}%)`);
            
            // 保存测试结果
            this.testResults.set(Date.now(), overallResult);

            return overallResult;

        } catch (error) {
            console.error('❌ 功能验证测试失败:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 运行测试套件
     * @SERVICE 测试套件运行方法
     * @param {string} suiteName - 测试套件名称
     * @returns {Promise<Object>} 套件测试结果
     */
    async runTestSuite(suiteName) {
        const suite = this.testSuites.get(suiteName);
        if (!suite) {
            throw new Error(`测试套件不存在: ${suiteName}`);
        }

        const results = [];
        let passedTests = 0;
        let failedTests = 0;

        for (const testName of suite.tests) {
            try {
                console.log(`  🧪 运行测试: ${testName}`);
                
                const testResult = await this.runSingleTest(testName);
                results.push(testResult);
                
                if (testResult.passed) {
                    passedTests++;
                    console.log(`    ✅ ${testName} 通过`);
                } else {
                    failedTests++;
                    console.log(`    ❌ ${testName} 失败: ${testResult.error}`);
                }
                
            } catch (error) {
                failedTests++;
                results.push({
                    testName: testName,
                    passed: false,
                    error: error.message,
                    duration: 0
                });
                console.log(`    ❌ ${testName} 异常: ${error.message}`);
            }
        }

        return {
            suiteName: suiteName,
            totalTests: suite.tests.length,
            passedTests: passedTests,
            failedTests: failedTests,
            results: results
        };
    }

    /**
     * 运行单个测试
     * @SERVICE 单个测试运行方法
     * @param {string} testName - 测试名称
     * @returns {Promise<Object>} 测试结果
     */
    async runSingleTest(testName) {
        const startTime = performance.now();
        
        try {
            // 根据测试名称调用对应的测试方法
            const testMethod = this[testName];
            if (typeof testMethod !== 'function') {
                throw new Error(`测试方法不存在: ${testName}`);
            }

            const result = await testMethod.call(this);
            const endTime = performance.now();

            return {
                testName: testName,
                passed: true,
                result: result,
                duration: endTime - startTime
            };

        } catch (error) {
            const endTime = performance.now();
            
            return {
                testName: testName,
                passed: false,
                error: error.message,
                duration: endTime - startTime
            };
        }
    }

    // ==================== 具体测试方法 ====================

    /**
     * 测试核心模块加载
     * @TEST 核心模块加载测试
     */
    async testCoreModulesLoaded() {
        const requiredModules = [
            'utils.js',
            'constants.js',
            'service-container.js',
            'event-bus.js',
            'storage-manager.js',
            'data-processor.js'
        ];

        const loadedModules = window.ModuleExports ? Object.keys(window.ModuleExports) : [];
        const missingModules = requiredModules.filter(module => !loadedModules.includes(module));

        if (missingModules.length > 0) {
            throw new Error(`缺少核心模块: ${missingModules.join(', ')}`);
        }

        return { loadedModules: loadedModules.length, requiredModules: requiredModules.length };
    }

    /**
     * 测试UI组件加载
     * @TEST UI组件加载测试
     */
    async testUIComponentsLoaded() {
        const uiModules = [
            'notification.js',
            'modal.js',
            'tabs.js',
            'progress.js'
        ];

        const loadedModules = window.ModuleExports ? Object.keys(window.ModuleExports) : [];
        const missingUI = uiModules.filter(module => !loadedModules.includes(module));

        if (missingUI.length > 0) {
            throw new Error(`缺少UI组件: ${missingUI.join(', ')}`);
        }

        return { uiModules: uiModules.length };
    }

    /**
     * 测试高级功能加载
     * @TEST 高级功能加载测试
     */
    async testAdvancedFeaturesLoaded() {
        const advancedModules = [
            'enhanced-upload.js',
            'qa-optimization.js',
            'report-generator.js'
        ];

        // 高级功能可能是懒加载的，所以只检查是否可以访问
        const mainApp = this.mainApp;
        if (!mainApp) {
            throw new Error('主应用未初始化');
        }

        return { mainAppInitialized: true };
    }

    /**
     * 测试文件选择
     * @TEST 文件选择测试
     */
    async testFileSelection() {
        const fileInput = document.getElementById('file-input');
        if (!fileInput) {
            throw new Error('文件输入元素不存在');
        }

        // 检查文件输入属性
        if (fileInput.type !== 'file') {
            throw new Error('文件输入类型不正确');
        }

        if (!fileInput.accept.includes('.txt')) {
            throw new Error('文件输入不接受txt文件');
        }

        return { fileInputExists: true, acceptsTxt: true };
    }

    /**
     * 测试拖拽上传
     * @TEST 拖拽上传测试
     */
    async testDragAndDrop() {
        const dropZone = document.getElementById('drop-zone');
        if (!dropZone) {
            throw new Error('拖拽区域不存在');
        }

        // 检查拖拽事件监听器（通过检查是否有相关的事件处理）
        const hasDropHandler = dropZone.ondrop !== null || 
                              dropZone.getAttribute('ondrop') !== null;

        return { dropZoneExists: true, hasDropHandler: hasDropHandler };
    }

    /**
     * 测试文件验证
     * @TEST 文件验证测试
     */
    async testFileValidation() {
        // 模拟文件验证逻辑
        const testFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
        
        // 检查文件类型验证
        const isValidType = testFile.type === 'text/plain' || testFile.name.endsWith('.txt');
        
        if (!isValidType) {
            throw new Error('文件类型验证失败');
        }

        return { fileValidation: true };
    }

    /**
     * 测试批量上传
     * @TEST 批量上传测试
     */
    async testBatchUpload() {
        // 检查主应用是否有批量处理方法
        if (!this.mainApp || typeof this.mainApp.processBatchFiles !== 'function') {
            throw new Error('批量上传功能不可用');
        }

        return { batchUploadAvailable: true };
    }

    /**
     * 测试文本解析
     * @TEST 文本解析测试
     */
    async testTextParsing() {
        // 检查解析器模块
        const parserModule = window.ModuleExports && window.ModuleExports['parser.js'];
        if (!parserModule) {
            throw new Error('解析器模块未加载');
        }

        // 检查解析方法
        if (typeof parserModule.parseTxtContent !== 'function') {
            throw new Error('文本解析方法不存在');
        }

        return { parserAvailable: true };
    }

    /**
     * 测试数据存储
     * @TEST 数据存储测试
     */
    async testDataStorage() {
        // 检查存储模块
        const storageModule = window.ModuleExports && window.ModuleExports['storage.js'];
        if (!storageModule) {
            throw new Error('存储模块未加载');
        }

        // 测试localStorage可用性
        try {
            localStorage.setItem('test', 'value');
            localStorage.removeItem('test');
        } catch (error) {
            throw new Error('localStorage不可用');
        }

        return { storageAvailable: true };
    }

    /**
     * 测试数据导出
     * @TEST 数据导出测试
     */
    async testDataExport() {
        // 检查导出功能
        const storageModule = window.ModuleExports && window.ModuleExports['storage.js'];
        if (!storageModule || typeof storageModule.exportToCSV !== 'function') {
            throw new Error('数据导出功能不可用');
        }

        return { exportAvailable: true };
    }

    /**
     * 测试通知系统
     * @TEST 通知系统测试
     */
    async testNotificationSystem() {
        // 检查通知管理器
        if (!this.mainApp || !this.mainApp.notificationManager) {
            throw new Error('通知管理器不可用');
        }

        return { notificationSystemAvailable: true };
    }

    /**
     * 测试模态框系统
     * @TEST 模态框系统测试
     */
    async testModalSystem() {
        // 检查模态框管理器
        if (!this.mainApp || !this.mainApp.modalManager) {
            throw new Error('模态框管理器不可用');
        }

        return { modalSystemAvailable: true };
    }

    /**
     * 测试标签页系统
     * @TEST 标签页系统测试
     */
    async testTabSystem() {
        // 检查标签页容器
        const tabsContainer = document.querySelector('.tabs-container');
        if (!tabsContainer) {
            throw new Error('标签页容器不存在');
        }

        return { tabSystemAvailable: true };
    }

    /**
     * 测试进度系统
     * @TEST 进度系统测试
     */
    async testProgressSystem() {
        // 检查进度管理器
        if (!this.mainApp || !this.mainApp.progressManager) {
            throw new Error('进度管理器不可用');
        }

        return { progressSystemAvailable: true };
    }

    /**
     * 测试报告创建
     * @TEST 报告创建测试
     */
    async testReportCreation() {
        // 检查报告生成器
        if (!this.mainApp || !this.mainApp.reportGenerator) {
            throw new Error('报告生成器不可用');
        }

        return { reportCreationAvailable: true };
    }

    /**
     * 测试报告导出
     * @TEST 报告导出测试
     */
    async testReportExport() {
        // 检查导出功能
        if (!this.mainApp || typeof this.mainApp.exportResults !== 'function') {
            throw new Error('报告导出功能不可用');
        }

        return { reportExportAvailable: true };
    }

    /**
     * 测试报告历史
     * @TEST 报告历史测试
     */
    async testReportHistory() {
        // 检查历史功能
        const storageManager = this.mainApp && this.mainApp.storageManager;
        if (!storageManager) {
            throw new Error('存储管理器不可用');
        }

        return { reportHistoryAvailable: true };
    }

    /**
     * 获取测试历史
     * @SERVICE 测试历史获取方法
     * @returns {Array} 历史测试结果
     */
    getTestHistory() {
        return Array.from(this.testResults.entries()).map(([timestamp, results]) => ({
            timestamp: new Date(timestamp).toISOString(),
            results: results
        }));
    }

    /**
     * 清除测试历史
     * @SERVICE 测试历史清除方法
     */
    clearTestHistory() {
        this.testResults.clear();
        console.log('🧹 测试历史已清除');
    }

    /**
     * 销毁功能测试器
     * @LIFECYCLE 功能测试器销毁方法
     */
    destroy() {
        this.clearTestHistory();
        this.testSuites.clear();
        this.mainApp = null;
        console.log('🗑️ FunctionalTester 已销毁');
    }
}

// ==================== 全局实例 ====================
let globalFunctionalTester = null;

/**
 * 获取全局功能测试器实例
 * @SERVICE 全局功能测试器获取函数
 * @returns {FunctionalTester} 功能测试器实例
 */
export function getFunctionalTester() {
    if (!globalFunctionalTester) {
        globalFunctionalTester = new FunctionalTester();
    }
    return globalFunctionalTester;
}

// ==================== 便捷函数 ====================

/**
 * 运行功能验证测试
 * @SERVICE 功能验证测试运行函数
 * @returns {Promise<Object>} 测试结果
 */
export async function runFunctionalTest() {
    return await getFunctionalTester().runFullFunctionalTest();
}

/**
 * 快速功能检查
 * @SERVICE 快速功能检查函数
 * @returns {Object} 检查结果
 */
export function quickFunctionalCheck() {
    const tester = getFunctionalTester();
    
    return {
        mainAppInitialized: !!tester.mainApp,
        modulesLoaded: window.ModuleExports ? Object.keys(window.ModuleExports).length : 0,
        domElementsPresent: {
            fileInput: !!document.getElementById('file-input'),
            dropZone: !!document.getElementById('drop-zone'),
            tabsContainer: !!document.querySelector('.tabs-container')
        },
        timestamp: Date.now()
    };
}
