/* storage-service-health.js (Async)
 * Usage: await window.StorageServiceHealth.run()
 * Prints interface completeness & basic size metrics.
 */
(function(){
    const REQUIRED_METHODS = [
        'loadAnalysisResults','saveAnalysisResults',
        'loadQADataset','saveQADataset',
        'loadKnowledgeData','saveKnowledgeData',
        'loadDrivers','saveDrivers',
        'getStorageInfo','exportAnalysisResultsCSV'
    ];

    async function run(){
        const svc = window.ModuleExports?.['storage-service.js']?.storageService;
        if(!svc){
            console.warn('[StorageHealth] storageService 未找到');
            return { ok:false, missing:['<service object>']};
        }
        const missing = REQUIRED_METHODS.filter(m => typeof svc[m] !== 'function');
        if(missing.length){
            console.warn('[StorageHealth] Missing methods:', missing);
            return { ok:false, missing };
        }
        let analyses = [], qa = [], knowledge = [], drivers = [], info = null;
        try {
            [info, analyses, qa, knowledge, drivers] = await Promise.all([
                svc.getStorageInfo().catch(e=>({ error:e.message })),
                svc.loadAnalysisResults().catch(()=>[]),
                svc.loadQADataset().catch(()=>[]),
                svc.loadKnowledgeData().catch(()=>[]),
                svc.loadDrivers().catch(()=>[])
            ]);
        } catch(e){
            console.error('[StorageHealth] probe failed', e);
            return { ok:false, error:e.message };
        }
        const sample = {
            analysisCount: analyses.length,
            qaCount: qa.length,
            knowledgeCount: knowledge.length,
            driversCount: drivers.length
        };
        const ok = !info?.error;
        if(!ok) console.warn('[StorageHealth] getStorageInfo error:', info.error);
        console.log('[StorageHealth] OK:', ok, 'Sample sizes:', sample);
        if(info && !info.error) console.log('[StorageHealth] Storage totalSize:', info.totalSize, 'bytes');
        return { ok, sample, info };
    }

    window.StorageServiceHealth = { run };
})();
