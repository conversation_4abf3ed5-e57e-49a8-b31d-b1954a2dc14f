# 📚 Memory Bank - 项目记忆库

## 🎯 记忆库概述

这个记忆库包含了司机客服对话分析系统重构项目的所有关键信息、决策记录和上下文数据。它确保项目的连续性和知识传承。

## 📁 文件结构说明

### 核心记忆文件
- **projectbrief.md** - 项目核心需求和目标定义
- **productContext.md** - 项目存在的原因和解决的问题
- **systemPatterns.md** - 系统架构和关键技术决策
- **techContext.md** - 技术栈和开发环境设置
- **activeContext.md** - 当前工作焦点和下一步计划
- **progress.md** - 项目进度跟踪和已知问题

### 实施计划目录
- **implementation-plans/** - 保存的重构规划文档
  - SOLUTION_PLAN.md - 完整方案计划书
  - ARCHITECTURE_DIAGRAMS.md - 架构图表集合
  - IMPLEMENTATION_GUIDE.md - 详细实施指南
  - TECHNICAL_SPECIFICATIONS.md - 技术规格说明
  - PROJECT_OVERVIEW.md - 项目概览

## 🔄 使用指南

### 开始新会话时
1. 读取所有核心记忆文件
2. 检查activeContext.md了解当前状态
3. 查看progress.md了解最新进展

### 更新记忆库
- 重要决策 → systemPatterns.md
- 技术变更 → techContext.md
- 进度更新 → progress.md
- 工作焦点变化 → activeContext.md

### 相关性评分系统
- [RS:5] 关键信息（当前优先级、关键偏好）
- [RS:4] 高重要性（活跃任务、最近决策）
- [RS:3] 中等重要性（一般背景、既定模式）
- [RS:2] 背景信息（历史上下文、过去决策）
- [RS:1] 外围信息（次要细节、过时信息）

## 📝 维护原则

1. **及时更新** - 重要变更立即记录
2. **结构化** - 使用统一的格式和标签
3. **相关性** - 为信息分配适当的重要性等级
4. **简洁性** - 保持信息精炼和可操作
