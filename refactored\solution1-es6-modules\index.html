<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire 对话分析平台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="src/styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header" role="banner">
        <div class="container">
            <h1 class="app-title">
                <i class="fas fa-chart-line" aria-hidden="true"></i>
                GoMyHire 对话分析平台 (模块化版)
            </h1>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main" role="main">
        <div class="container">
            <!-- 加载状态 -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>正在加载模块...</p>
                <div id="loading-progress" style="margin-top: 10px; font-size: 12px; color: #666;"></div>
            </div>

            <!-- 主应用界面 -->
            <div id="main-app" style="display: none;">
                <!-- 功能导航标签页 -->
                <nav class="tabs-container" role="navigation" aria-label="主要功能导航">
                    <div class="tabs-nav" role="tablist">
                        <button type="button" class="tab-btn active" data-tab="analysis" role="tab" aria-selected="true">
                            <i class="fas fa-chart-line"></i> 数据分析
                        </button>
                        <button type="button" class="tab-btn" data-tab="reports" role="tab" aria-selected="false">
                            <i class="fas fa-file-alt"></i> 详细报告
                        </button>
                        <button type="button" class="tab-btn" data-tab="qa-dataset" role="tab" aria-selected="false">
                            <i class="fas fa-question-circle"></i> 问答题集
                        </button>
                        <button type="button" class="tab-btn" data-tab="settings" role="tab" aria-selected="false">
                            <i class="fas fa-cog"></i> 系统设置
                        </button>
                    </div>
                </nav>

                <!-- 数据分析标签页 -->
                <section class="tab-content active" id="analysis-panel" role="tabpanel">
                    <!-- 文件上传区域 -->
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-upload"></i> 文件上传</h2>
                        </div>
                        <div class="card-body">
                            <div id="drop-zone" class="drop-zone">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>拖拽文件到此处或点击选择文件</p>
                                <button type="button" id="select-files-btn" class="btn-primary">选择文件</button>
                                <input type="file" id="file-input" multiple accept=".txt" style="display: none;">
                            </div>
                        </div>
                    </div>

                    <!-- 统计概览 -->
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-chart-bar"></i> 统计概览</h2>
                        </div>
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="main-drivers-count">0</div>
                                        <div class="stat-label">司机数量</div>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-icon"><i class="fas fa-headset"></i></div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="main-agents-count">0</div>
                                        <div class="stat-label">客服数量</div>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-icon"><i class="fas fa-book"></i></div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="main-knowledge-count">0</div>
                                        <div class="stat-label">知识库条目</div>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-icon"><i class="fas fa-star"></i></div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="main-avg-satisfaction">0</div>
                                        <div class="stat-label">平均满意度</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据可视化图表 -->
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-chart-pie"></i> 数据可视化</h2>
                        </div>
                        <div class="card-body">
                            <div class="charts-grid">
                                <div class="chart-container">
                                    <div id="chart-questions" class="chart"></div>
                                    <h4>问题分布</h4>
                                </div>
                                <div class="chart-container">
                                    <div id="chart-effectiveness" class="chart"></div>
                                    <h4>有效性分析</h4>
                                </div>
                                <div class="chart-container">
                                    <div id="chart-satisfaction" class="chart"></div>
                                    <h4>满意度统计</h4>
                                </div>
                                <div class="chart-container">
                                    <div id="chart-knowledge" class="chart"></div>
                                    <h4>知识库覆盖</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析控制 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="analysis-controls">
                                <button type="button" id="start-analysis-btn" class="btn-primary" disabled>
                                    <i class="fas fa-play"></i> 开始分析
                                </button>
                                <button type="button" id="export-results-btn" class="btn-secondary">
                                    <i class="fas fa-download"></i> 导出结果
                                </button>
                                <button type="button" id="clear-data-btn" class="btn-outline-danger">
                                    <i class="fas fa-trash"></i> 清空数据
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 其他标签页内容占位符 -->
                <section class="tab-content" id="reports-panel" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-file-alt"></i> 详细报告</h2>
                        </div>
                        <div class="card-body">
                            <p>详细报告功能开发中...</p>
                        </div>
                    </div>
                </section>

                <section class="tab-content" id="qa-dataset-panel" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-question-circle"></i> 问答题集</h2>
                        </div>
                        <div class="card-body">
                            <p>问答题集功能开发中...</p>
                        </div>
                    </div>
                </section>

                <section class="tab-content" id="settings-panel" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-cog"></i> 系统设置</h2>
                        </div>
                        <div class="card-body">
                            <div class="settings-grid">
                                <div class="setting-item">
                                    <label for="api-key-input">Kimi API密钥</label>
                                    <input type="password" id="api-key-input" class="form-control" placeholder="sk-your-api-key-here">
                                </div>
                                <div class="setting-item">
                                    <label for="max-concurrency-input">最大并发数</label>
                                    <input type="number" id="max-concurrency-input" class="form-control" value="50" min="1" max="100">
                                </div>
                                <div class="setting-item">
                                    <button type="button" id="save-settings-btn" class="btn-primary">
                                        <i class="fas fa-save"></i> 保存设置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- 进度显示 -->
            <div id="upload-status" class="upload-status hidden">
                <div class="upload-status-header">
                    <h4 id="upload-status-title">处理中...</h4>
                    <button type="button" id="upload-cancel-btn" class="upload-cancel-btn">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
                <div class="upload-progress-bar">
                    <div id="upload-progress-fill" class="upload-progress-fill"></div>
                </div>
                <div id="upload-progress-text" class="upload-progress-text">准备中...</div>
            </div>

            <!-- 模块测试状态面板 -->
            <div id="module-status" style="display: none; margin-top: 20px;">
                <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 15px; color: #333;">
                        <i class="fas fa-cogs"></i> 模块加载状态
                    </h3>
                    <div id="module-list" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                        <!-- 模块状态将在这里动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 集成测试面板 -->
            <div id="integration-test" style="display: none; margin-top: 20px;">
                <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 15px; color: #333;">
                        <i class="fas fa-flask"></i> 集成测试
                    </h3>
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button id="test-modules" class="btn" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            测试模块接口
                        </button>
                        <button id="test-dependencies" class="btn" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            测试依赖关系
                        </button>
                        <button id="test-storage" class="btn" style="background: #ffc107; color: black; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            测试存储功能
                        </button>
                        <button id="test-parser" class="btn" style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            测试解析功能
                        </button>
                    </div>
                    <div id="test-results" style="background: #f8f9fa; border-radius: 4px; padding: 15px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- CDN库加载 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>

    <!-- 模块加载器 -->
    <script src="./loader.js"></script>
</body>
</html>