/* === Storage Service (Refactor Phase 1) ===
 * 目标: 提供统一的 IStorageService 接口, 隐藏内部存储实现细节。
 * 阶段: Phase 1 抽象层引入 (兼容旧 storage.js, 不破坏现有全局导出)。
 * 使用方式 (过渡期):
 *   const storageService = window.ModuleExports?.['storage-service.js']?.storageService;
 *   storageService.loadAnalysisResults();
 * 未来阶段: main.js 与其它模块改为仅依赖 storageService; storage.js 退化为兼容壳或移除。
 */
(function initStorageService(){
    const globalRef = window;

    const KEYS = (globalRef.CONSTANTS && globalRef.CONSTANTS.STORAGE_KEYS) || {};
    const ls = globalRef.localStorage;

    function safeParse(raw, def){
        if(!raw) return def;
        try { return JSON.parse(raw); } catch(e){ return def; }
    }
    function safeStringify(obj){
        try { return JSON.stringify(obj); } catch(e){ return 'null'; }
    }

    function load(key, def){
        if(!ls) return def;
        return safeParse(ls.getItem(key), def);
    }
    function save(key, value){
        if(!ls) return false;
        ls.setItem(key, safeStringify(value));
        return true;
    }

    function ensureTimestamped(result){
        if(!result) return result;
        if(!result.id || !result.timestamp){
            const ts = Date.now();
            return { ...result, id: result.id || `analysis_${ts}`, timestamp: result.timestamp || ts };
        }
        return result;
    }

    const storageService = {
        // ---- Analysis Results (Promise API) ----
        loadAnalysisResults(){
            return Promise.resolve(load(KEYS.DETAILED_QUESTIONS, []));
        },
        saveAnalysisResults(result){
            return Promise.resolve().then(()=>{
                const list = load(KEYS.DETAILED_QUESTIONS, []);
                list.push(ensureTimestamped(result));
                save(KEYS.DETAILED_QUESTIONS, list);
                return list;
            });
        },

        // ---- QA Dataset ----
        loadQADataset(){ return Promise.resolve(load(KEYS.QA_DATASET, [])); },
        saveQADataset(data){ return Promise.resolve(save(KEYS.QA_DATASET, data || [])); },

        // ---- Knowledge ----
        loadKnowledgeData(){ return Promise.resolve(load(KEYS.KNOWLEDGE, [])); },
        saveKnowledgeData(data){ return Promise.resolve(save(KEYS.KNOWLEDGE, data || [])); },

        // ---- Metrics / Drivers ----
        loadDrivers(){ return Promise.resolve(load(KEYS.DRIVERS, [])); },
        saveDrivers(data){ return Promise.resolve(save(KEYS.DRIVERS, data || [])); },

        // ---- Info ----
        getStorageInfo(){
            if(!ls) return Promise.resolve({ available:false });
            let totalSize = 0; const itemSizes = {};
            Object.values(KEYS).forEach(k=>{ const v = ls.getItem(k); const size = v? new Blob([v]).size:0; totalSize += size; itemSizes[k]=size; });
            return Promise.resolve({ available:true, totalSize, itemSizes });
        },

        // ---- CSV Export ----
        exportAnalysisResultsCSV(filename = 'analysis_results.csv'){
            return this.loadAnalysisResults().then(data => {
                if(!data || data.length===0){ console.warn('[StorageService] 无分析结果可导出'); return null; }
                const headers = ['文件名','对话数','消息数','分析时间','有效性','满意度','问题分类'];
                const rows = data.map(item => [
                    item.fileName || '',
                    item.stats?.totalConversations || 0,
                    item.stats?.totalMessages || 0,
                    item.timestamp ? new Date(item.timestamp).toLocaleString() : '',
                    item.stats?.avgEffectiveness || 0,
                    item.stats?.avgSatisfaction || 0,
                    (item.stats?.topCategories || []).join('|')
                ]);
                const csv = [headers.join(','), ...rows.map(r=> r.map(v=>`"${String(v).replace(/"/g,'""')}"`).join(','))].join('\n');
                const blob = new Blob(['\ufeff'+csv], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                return csv;
            });
        }
    };

    globalRef.ModuleExports = globalRef.ModuleExports || {};
    globalRef.ModuleExports['storage-service.js'] = { storageService };
})();
