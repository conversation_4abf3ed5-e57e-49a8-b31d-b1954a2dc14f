/* === Naming Conventions Header (auto-injected 2025-08-14) ===
    Parser constants: PARSER_* prefix.
*/
/**
 * ==================== GoMyHire 对话分析系统 - 文本解析模块 ====================
 * 
 * @MODULE_INFO
 * 模块名称: Text Parser System
 * 版本: 2.0.0
 * 功能描述: 专门解析对话文本文件，提取用户信息、消息内容和时间戳
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: constants.js (REGEX_PATTERNS), utils.js (隐式依赖)
 * 间接依赖: 无
 * 被依赖: main.js, data-processor.js, ui.js
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 2 (第三层，核心功能模块)
 * 加载时机: after-utils (工具模块加载后)
 * 加载条件: 依赖constants.js加载完成
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - 文本解析器类 (TextParser类)
 *   - 时间戳解析
 *   - 对话数据提取
 *   - 消息格式化
 *   - 缓存管理
 * 导出接口: TextParser, getTextParser
 * 全局注册: window.ModuleExports['parser.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 原始文本内容、文件名
 * 输出数据: 结构化对话数据、解析统计
 * 状态管理: 维护解析缓存、模式匹配统计
 * 事件处理: 解析完成事件
 * 
 * @INTEGRATION (集成关系)
 * 数据集成: 为data-processor提供解析数据
 * UI集成: 为UI提供解析进度反馈
 * 存储集成: 将解析结果传递给存储系统
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 低 (轻量级缓存)
 * 加载性能: 快速 (无外部依赖)
 * 运行时性能: 高效 (优化的正则匹配)
 */

class TextParser {
    constructor() {
        this.patterns = window.CONSTANTS?.REGEX_PATTERNS || {
            TIME_STAMP: /(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})/,
            SPEAKER_NAME: /^([^:：]+)[：:]\s*/
        };

        // 结果缓存
        this.parseCache = new Map();
        this.analysisCache = new Map();
        this.maxCacheSize = 100;
    }

    /**
     * 解析时间戳字符串为Unix时间戳
     * 输入格式(DD/MM/YYYY)的日期字符串(HH:mm:ss)转换为Unix时间戳
     */
    parseTimestamp(dateStr, timeStr) {
        if (!dateStr || !timeStr) return 0;
        try {
            const [day, month, year] = dateStr.split('/');
            const [hour, minute, second] = timeStr.split(':');

            const date = new Date(
                parseInt(year),
                parseInt(month) - 1,
                parseInt(day),
                parseInt(hour),
                parseInt(minute),
                parseInt(second || 0)
            );

            return date.getTime();
        } catch (error) {
            return 0;
        }
    }

    /**
     * 解析txt文件内容 - 基于真实格式
     * 解析对话记录文件，提取用户信息、消息内容和时间戳
     */
    parseTxtContent(content, fileName = 'unknown') {
        // 检查缓存
        const cacheKey = this.generateCacheKey(content, fileName);
        if (this.parseCache.has(cacheKey)) {
            return this.parseCache.get(cacheKey);
        }

        const conversations = [];
        let currentConversation = null;

        const lines = content.split('\n');
        let messageBuffer = [];

        // 逐行解析内容
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // 检测分隔符
            if (line.includes('-----------------------------------------------')) {
                if (messageBuffer.length > 0 && currentConversation) {
                    // 保存当前消息
                    const messageContent = messageBuffer.join('\n').trim();
                    if (messageContent) {
                        currentConversation.messages.push({
                            content: messageContent,
                            timestamp: currentConversation.currentTimestamp || 0
                        });
                    }
                    messageBuffer = [];
                }
                continue;
            }

            // 检测时间戳行
            if (line.startsWith('Time:')) {
                const timeMatch = line.match(/Time:\s*(.+)/);
                if (timeMatch) {
                    const [dateStr, timeStr] = timeMatch[1].split(' ');
                    const timestamp = this.parseTimestamp(dateStr, timeStr);

                    // 开始新的对话块
                    if (currentConversation && currentConversation.messages.length > 0) {
                        conversations.push(currentConversation);
                    }

                    currentConversation = {
                        fileName,
                        timestamp,
                        currentTimestamp: timestamp,
                        messages: [],
                        user: null,
                        metadata: {}
                    };
                }
                continue;
            }

            // 检测用户信息
            if (line.startsWith('User:')) {
                const userMatch = line.match(/User:\s*(.+)/);
                if (userMatch && currentConversation) {
                    currentConversation.user = userMatch[1];

                    // 解析用户角色
                    if (userMatch[1].includes('Driver:')) {
                        currentConversation.role = 'driver';
                        currentConversation.driverName = userMatch[1].replace('Driver:', '').trim();
                    } else if (userMatch[1].includes('Support:')) {
                        currentConversation.role = 'support';
                        currentConversation.supportAgent = userMatch[1].replace('Support:', '').trim();
                    }
                }
                continue;
            }

            // 检测消息内容
            if (line.startsWith('Message:')) {
                const messageMatch = line.match(/Message:\s*(.+)/);
                if (messageMatch) {
                    messageBuffer.push(messageMatch[1]);
                }
                continue;
            }

            // 检测元数据字段
            const metadataFields = ['Date:', 'From:', 'To:', 'Cartype:'];
            for (const field of metadataFields) {
                if (line.startsWith(field)) {
                    const value = line.replace(field, '').trim();
                    if (currentConversation && value) {
                        const key = field.replace(':', '').toLowerCase();
                        currentConversation.metadata[key] = value;
                    }
                    break;
                }
            }

            // 续行处理
            if (line && !line.startsWith('Time:') && !line.startsWith('User:') &&
                !line.startsWith('Message:') && !metadataFields.some(f => line.startsWith(f))) {
                if (messageBuffer.length > 0) {
                    messageBuffer.push(line);
                }
            }
        }

        // 处理最后一个对话
        if (currentConversation && messageBuffer.length > 0) {
            const messageContent = messageBuffer.join('\n').trim();
            if (messageContent) {
                currentConversation.messages.push({
                    content: messageContent,
                    timestamp: currentConversation.currentTimestamp || 0
                });
            }
            conversations.push(currentConversation);
        }

        const stats = {
            totalConversations: conversations.length,
            driverConversations: conversations.filter(c => c.role === 'driver').length,
            supportConversations: conversations.filter(c => c.role === 'support').length,
            totalMessages: conversations.reduce((sum, c) => sum + c.messages.length, 0),
            parseTimestamp: Date.now()
        };

        const result = {
            fileName,
            conversations,
            stats,
            timestamp: Date.now()
        };

        // 存储到缓存
        this.setCacheItem(this.parseCache, cacheKey, result);

        return result;
    }

    /**
     * 生成缓存键
     */
    generateCacheKey(content, fileName) {
        const hash = this.simpleHash(content + fileName);
        return `${fileName}_${hash}`;
    }

    /**
     * 简单哈希函数
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(36);
    }

    /**
     * 设置缓存项（带LRU清理）
     */
    setCacheItem(cache, key, value) {
        if (cache.size >= this.maxCacheSize) {
            const firstKey = cache.keys().next().value;
            cache.delete(firstKey);
        }
        cache.set(key, value);
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.parseCache.clear();
        this.analysisCache.clear();
    }

    /**
     * 获取缓存统计
     */
    getCacheStats() {
        return {
            parseCache: this.parseCache.size,
            analysisCache: this.analysisCache.size,
            maxSize: this.maxCacheSize
        };
    }

    /**
     * 验证解析结果
     */
    validateParseResult(parseResult) {
        const errors = [];
        const warnings = [];

        if (!parseResult || !parseResult.conversations) {
            errors.push('解析结果为空或格式错误');
            return { isValid: false, errors, warnings };
        }

        const { conversations, stats } = parseResult;

        // 检查对话数量
        if (conversations.length === 0) {
            warnings.push('未找到任何对话记录');
        }

        // 检查消息数量
        if (stats.totalMessages === 0) {
            warnings.push('未找到任何消息内容');
        }

        // 检查时间戳
        const invalidTimestamps = conversations.filter(c => !c.timestamp || c.timestamp === 0);
        if (invalidTimestamps.length > 0) {
            warnings.push(`${invalidTimestamps.length} 个对话缺少有效时间戳`);
        }

        // 检查用户信息
        const missingUsers = conversations.filter(c => !c.user);
        if (missingUsers.length > 0) {
            warnings.push(`${missingUsers.length} 个对话缺少用户信息`);
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            stats: {
                totalConversations: conversations.length,
                validConversations: conversations.filter(c => c.timestamp && c.user).length,
                totalMessages: stats.totalMessages
            }
        };
    }

    /**
     * 使用Kimi API进行对话分析
     */
    async evaluateConversationWithKimi(conversationData, apiKey) {
        try {
            console.log('[Parser] Starting Kimi API analysis...');

            if (!apiKey) {
                throw new Error('API密钥未配置');
            }

            // 构建分析提示词
            const prompt = this.buildAnalysisPrompt(conversationData);

            const requestBody = {
                model: 'kimi-k2-turbo-preview',
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的客服对话分析师，请分析以下对话并给出评分和分类。'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 1000
            };

            const response = await fetch('https://api.moonshot.cn/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify(requestBody),
                signal: AbortSignal.timeout(90000) // 90秒超时
            });

            if (!response.ok) {
                throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            const analysisText = result.choices[0]?.message?.content;

            if (!analysisText) {
                throw new Error('API返回结果为空');
            }

            // 解析AI返回的分析结果
            const analysis = this.parseAIAnalysis(analysisText);

            console.log('[Parser] Kimi API analysis completed');
            return analysis;

        } catch (error) {
            console.error(`[Parser] Kimi API analysis failed: ${error.message}`);

            // 返回默认分析结果
            return {
                effectiveness: 3,
                satisfaction: 3,
                knowledge_covered: 3,
                question_category: '其他',
                error: error.message
            };
        }
    }

    /**
     * 构建分析提示词
     */
    buildAnalysisPrompt(conversationData) {
        const messages = conversationData.messages || [];
        const conversationText = messages.map(msg =>
            `${msg.content}`
        ).join('\n');

        return `
请分析以下客服对话，并按照JSON格式返回分析结果：

对话内容：
${conversationText}

请返回以下格式的JSON：
{
    "effectiveness": 1-5的整数评分（客服回复的有效性），
    "satisfaction": 1-5的整数评分（客户满意度），
    "knowledge_covered": 1-5的整数评分（知识覆盖程度），
    "question_category": "问题分类（如：订单问题、支付问题、路线咨询、服务投诉、技术支持、账户问题、其他）"
}
        `.trim();
    }

    /**
     * 解析AI分析结果
     */
    parseAIAnalysis(analysisText) {
        try {
            // 尝试提取JSON
            const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return {
                    effectiveness: Math.max(1, Math.min(5, parseInt(parsed.effectiveness) || 3)),
                    satisfaction: Math.max(1, Math.min(5, parseInt(parsed.satisfaction) || 3)),
                    knowledge_covered: Math.max(1, Math.min(5, parseInt(parsed.knowledge_covered) || 3)),
                    question_category: parsed.question_category || '其他'
                };
            }
        } catch (error) {
            console.warn('[Parser] Failed to parse AI analysis, using defaults');
        }

        // 默认返回值
        return {
            effectiveness: 3,
            satisfaction: 3,
            knowledge_covered: 3,
            question_category: '其他'
        };
    }

    /**
     * 批量处理文件
     */
    async parseBatchFiles(files, progressCallback = null) {
        const results = [];
        const totalFiles = files.length;

        console.log(`[Parser] Starting batch processing of ${totalFiles} files`);

        for (let i = 0; i < totalFiles; i++) {
            const file = files[i];

            try {
                // 更新进度
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: totalFiles,
                        fileName: file.name,
                        percentage: Math.round(((i + 1) / totalFiles) * 100)
                    });
                }

                // 读取文件内容
                const content = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = () => reject(reader.error);
                    reader.readAsText(file, 'utf-8');
                });

                // 解析内容
                const parseResult = this.parseTxtContent(content, file.name);
                const validation = this.validateParseResult(parseResult);

                results.push({
                    file: file.name,
                    parseResult,
                    validation,
                    success: validation.isValid
                });

            } catch (error) {
                console.error(`[Parser] Failed to process file: ${file.name}`, error);
                results.push({
                    file: file.name,
                    parseResult: null,
                    validation: { isValid: false, errors: [error.message], warnings: [] },
                    success: false
                });
            }
        }

        // 统计总体结果
        const summary = {
            totalFiles: totalFiles,
            successCount: results.filter(r => r.success).length,
            failureCount: results.filter(r => !r.success).length,
            totalConversations: results.reduce((sum, r) =>
                sum + (r.parseResult?.stats?.totalConversations || 0), 0),
            totalMessages: results.reduce((sum, r) =>
                sum + (r.parseResult?.stats?.totalMessages || 0), 0)
        };

        console.log(`[Parser] Batch processing completed:`, summary);

        return {
            results,
            summary
        };
    }
}

// ==================== 全局文本解析器实例 ====================
let textParserInstance = null;

/**
 * 获取全局文本解析器实例
 * @SERVICE 全局文本解析器获取函数
 * @returns {TextParser} 文本解析器实例
 */
function getTextParser() {
    if (!textParserInstance) {
        textParserInstance = new TextParser();
    }
    return textParserInstance;
}

// 创建默认全局实例
const textParser = getTextParser();

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['parser.js'] = {
    TextParser,
    getTextParser,
    textParser // 兼容性导出
};
