# GoMyHire 对话分析系统 - 用户指南

## 📋 目录

1. [系统概述](#系统概述)
2. [快速上手](#快速上手)
3. [功能详解](#功能详解)
4. [常见问题](#常见问题)
5. [故障排除](#故障排除)

## 系统概述

GoMyHire对话分析系统是一个专门用于分析司机与客服对话记录的智能工具。系统能够：

- 📁 批量处理对话文件
- 🤖 智能分析对话质量
- 📊 生成可视化报表
- 💾 导出分析结果

## 快速上手

### 第一步：打开应用

1. **直接打开方式**
   - 双击 `index.html` 文件
   - 系统会在默认浏览器中打开

2. **本地服务器方式（推荐）**
   ```bash
   # 在项目目录下运行
   python -m http.server 8000
   # 然后访问 http://localhost:8000
   ```

### 第二步：系统检查

应用启动后会自动进行系统检查：
- ✅ 模块加载状态
- ✅ 依赖关系验证
- ✅ 存储功能测试

### 第三步：配置API密钥（可选）

如需使用AI分析功能：
1. 复制 `config/local-config.example.js` 为 `config/local-config.js`
2. 编辑文件，填入你的Kimi API密钥
3. 刷新页面使配置生效

## 功能详解

### 1. 文件上传

#### 支持的文件格式
- ✅ `.txt` 文本文件
- ✅ 最大文件大小：50MB
- ✅ 批量上传：最多100个文件

#### 上传方式
1. **拖拽上传**
   - 将文件拖拽到上传区域
   - 支持多文件同时拖拽

2. **点击选择**
   - 点击上传区域
   - 在文件选择器中选择文件

#### 文件格式要求
对话文件应包含以下格式的内容：
```
-----------------------------------------------
Time: 02/01/2025 13:51:11
User: Driver: 张三
Message: 这张单是不是取消了？
-----------------------------------------------

-----------------------------------------------
Time: 02/01/2025 14:00:31
User: Support: 客服小李
Message: 请问订单号是多少？
-----------------------------------------------
```

### 2. 数据解析

#### 自动解析功能
- 🕐 **时间戳识别**: 自动提取对话时间
- 👤 **角色识别**: 区分司机和客服
- 💬 **消息提取**: 提取完整对话内容
- 📊 **统计计算**: 自动生成对话统计

#### 解析结果
- 总对话数量
- 司机对话数量
- 客服对话数量
- 总消息数量

### 3. AI智能分析

#### 分析维度
1. **有效性评分** (1-5分)
   - 客服回复的针对性
   - 问题解决程度

2. **满意度评分** (1-5分)
   - 客户情绪分析
   - 服务质量评估

3. **知识覆盖度** (1-5分)
   - 知识库匹配程度
   - 专业性评估

4. **问题分类**
   - 订单问题
   - 支付问题
   - 路线咨询
   - 服务投诉
   - 技术支持
   - 账户问题
   - 其他

#### 使用AI分析
1. 确保已配置API密钥
2. 上传对话文件
3. 系统自动调用AI进行分析
4. 查看分析结果和图表

### 4. 数据可视化

#### 图表类型
1. **问题类型分布** - 饼图
   - 显示各类问题的占比
   - 支持交互式查看

2. **有效性评分分布** - 柱状图
   - 1-5分的分布情况
   - 直观显示服务质量

3. **满意度分布** - 环形图
   - 客户满意度统计
   - 多层次数据展示

4. **知识库覆盖率** - 柱状图
   - 常见问题覆盖情况
   - 知识库完善度评估

#### 图表操作
- 🔍 **缩放**: 鼠标滚轮缩放
- 📱 **平移**: 拖拽移动视图
- 💾 **导出**: 右键保存图片
- 🔄 **刷新**: 实时更新数据

### 5. 数据导出

#### 导出格式
- 📊 **CSV格式**: 兼容Excel
- 🔤 **UTF-8编码**: 完美支持中文
- 📋 **标准格式**: 包含所有分析字段

#### 导出内容
- 文件名
- 对话数量
- 消息数量
- 分析时间
- 有效性评分
- 满意度评分
- 问题分类

#### 导出操作
1. 完成文件分析后
2. 点击"导出数据"按钮
3. 选择导出类型
4. 文件自动下载

### 6. 系统测试

#### 内置测试功能
1. **模块接口测试**
   - 验证所有模块正确加载
   - 检查接口完整性

2. **依赖关系测试**
   - 检查模块间依赖
   - 验证加载顺序

3. **存储功能测试**
   - 测试数据保存
   - 验证数据加载

4. **解析功能测试**
   - 使用真实数据测试
   - 验证解析准确性

#### 运行测试
1. 在测试面板中选择测试类型
2. 点击对应的测试按钮
3. 查看测试结果和详细信息

## 常见问题

### Q1: 文件上传失败怎么办？
**A**: 检查以下几点：
- 文件格式是否为.txt
- 文件大小是否超过50MB
- 文件内容是否符合格式要求

### Q2: AI分析不工作？
**A**: 请确认：
- 是否配置了有效的API密钥
- 网络连接是否正常
- API密钥是否有足够的额度

### Q3: 图表显示异常？
**A**: 可能的原因：
- ECharts库未正确加载
- 数据格式有误
- 浏览器兼容性问题

### Q4: 数据导出为空？
**A**: 检查：
- 是否有分析结果
- 浏览器是否允许下载
- 存储空间是否充足

### Q5: 页面加载缓慢？
**A**: 优化建议：
- 使用本地服务器运行
- 清理浏览器缓存
- 检查网络连接

## 故障排除

### 1. 模块加载失败
```
症状：页面显示"模块加载失败"
解决：
1. 检查文件完整性
2. 使用本地服务器运行
3. 检查浏览器控制台错误
```

### 2. 解析结果异常
```
症状：解析出的对话数量为0
解决：
1. 检查文件格式是否正确
2. 确认文件编码为UTF-8
3. 查看解析测试结果
```

### 3. 图表不显示
```
症状：图表区域空白
解决：
1. 检查ECharts库是否加载
2. 确认有分析数据
3. 尝试刷新页面
```

### 4. 性能问题
```
症状：处理大文件时卡顿
解决：
1. 分批处理文件
2. 清理浏览器缓存
3. 关闭其他标签页
```

### 5. 兼容性问题
```
症状：某些功能在特定浏览器不工作
解决：
1. 更新浏览器到最新版本
2. 尝试使用Chrome浏览器
3. 检查JavaScript是否启用
```

## 技术支持

如遇到其他问题，请：
1. 查看浏览器控制台错误信息
2. 运行内置测试功能诊断问题
3. 联系技术支持团队

---

**版本**: v2.0.0  
**最后更新**: 2025-08-13  
**技术支持**: Augment Agent
