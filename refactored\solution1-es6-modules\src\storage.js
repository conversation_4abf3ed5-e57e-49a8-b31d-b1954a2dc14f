/* storage.js (<PERSON> Facade Only)
 * Legacy implementation removed. For backward compatibility this exposes
 * a getter to storageService via the ModuleExports registry.
 */
(function(){
    window.ModuleExports = window.ModuleExports || {};
    window.ModuleExports['storage.js'] = {
        get storageService(){
            const svc = window.ModuleExports?.['storage-service.js']?.storageService;
            if(!svc) console.warn('[storage.js] storageService 未加载');
            return svc;
        },
        _note:'Slim facade – use storageService directly'
    };
})();
/**
 * ==================== GoMyHire 对话分析系统 - 数据存储管理模块 ====================
 * 
 * @MODULE_INFO
 * 模块名称: App Data Manager System
 * 版本: 2.0.0
 * 功能描述: 应用数据管理系统，管理各类应用数据的存储和检索
 * 
 * @DEPENDENCIES (依赖关系)
 * 直接依赖: constants.js (STORAGE_KEYS), 浏览器localStorage API
 * 间接依赖: 无
 * 被依赖: main.js, data-processor.js, ui.js, parser.js
 * 
 * @LOADING_PHASE (加载阶段)
 * 加载层级: 1 (第二层，核心数据模块)
 * 加载时机: early (早期加载)
 * 加载条件: 依赖constants.js加载完成
 * 
 * @FUNCTIONALITY (功能承载)
 * 主要功能:
 *   - 应用数据管理器类 (AppDataManager类)
 *   - 数据存储和检索
 *   - 历史数据迁移
 *   - 内存缓存管理
 *   - CSV导出功能
 * 导出接口: AppDataManager, getAppDataManager
 * 全局注册: window.ModuleExports['storage.js']
 * 
 * @DATA_FLOW (数据流)
 * 输入数据: 各类应用数据、存储键名
 * 输出数据: 存储的数据、CSV导出文件
 * 状态管理: 维护内存缓存、存储状态
 * 事件处理: 数据变更事件
 * 
 * @INTEGRATION (集成关系)
 * 浏览器集成: 与localStorage深度集成
 * 数据集成: 为数据处理模块提供存储服务
 * UI集成: 为UI组件提供数据支持
 * 
 * @PERFORMANCE (性能考虑)
 * 内存占用: 中等 (内存缓存系统)
 * 加载性能: 快速 (轻量级模块)
 * 运行时性能: 高效 (优化的缓存机制)
 */

// (Removed legacy AppDataManager body; historical content now tracked via CHANGELOG.)

// 创建默认全局实例
const appDataManager = getAppDataManager();

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['storage.js'] = {
    AppDataManager,
    getAppDataManager,
    appDataManager, // 兼容性导出
    // Phase 1: 提供统一 storageService 代理 (从 storage-service.js 获取) + 弃用提示
    get storageService() {
        const svc = window.ModuleExports?.['storage-service.js']?.storageService;
        if (!svc) {
            console.warn('[storage.js] storageService 尚未加载 (storage-service.js)');
        }
        return svc;
    },
    _deprecated: true,
    _note: 'Phase1: 请迁移到 window.ModuleExports[\'storage-service.js\'].storageService'
};
