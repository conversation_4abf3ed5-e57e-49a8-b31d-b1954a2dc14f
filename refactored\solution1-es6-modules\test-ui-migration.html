<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI迁移测试 - GoMyHire 对话分析系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="src/styles.css">
    <style>
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .test-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
        }
        
        .test-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .test-result {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 0.9em;
            min-height: 100px;
            white-space: pre-wrap;
        }
        
        .btn-test {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        
        .btn-test:hover { transform: translateY(-1px); }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1 class="app-title">
                <i class="fas fa-vial"></i>
                UI迁移功能测试
            </h1>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <!-- 通知系统测试 -->
            <div class="test-section">
                <div class="test-header">
                    <div class="test-title">🔔 通知系统测试</div>
                    <div class="test-status status-pending" id="notification-status">待测试</div>
                </div>
                <div class="test-controls">
                    <button class="btn-test btn-success" onclick="testNotification('success')">成功通知</button>
                    <button class="btn-test btn-danger" onclick="testNotification('error')">错误通知</button>
                    <button class="btn-test btn-warning" onclick="testNotification('warning')">警告通知</button>
                    <button class="btn-test btn-primary" onclick="testNotification('info')">信息通知</button>
                </div>
                <div class="test-result" id="notification-result">等待测试...</div>
            </div>

            <!-- 模态框系统测试 -->
            <div class="test-section">
                <div class="test-header">
                    <div class="test-title">🪟 模态框系统测试</div>
                    <div class="test-status status-pending" id="modal-status">待测试</div>
                </div>
                <div class="test-controls">
                    <button class="btn-test btn-primary" onclick="testModal()">显示模态框</button>
                    <button class="btn-test btn-warning" onclick="testModalWithFooter()">带按钮模态框</button>
                </div>
                <div class="test-result" id="modal-result">等待测试...</div>
            </div>

            <!-- 标签页系统测试 -->
            <div class="test-section">
                <div class="test-header">
                    <div class="test-title">📑 标签页系统测试</div>
                    <div class="test-status status-pending" id="tabs-status">待测试</div>
                </div>
                <div class="test-controls">
                    <button class="btn-test btn-primary" onclick="testTabSwitch('analysis')">分析标签</button>
                    <button class="btn-test btn-primary" onclick="testTabSwitch('reports')">报告标签</button>
                    <button class="btn-test btn-primary" onclick="testTabSwitch('settings')">设置标签</button>
                </div>
                <div class="test-result" id="tabs-result">等待测试...</div>
            </div>

            <!-- 进度显示测试 -->
            <div class="test-section">
                <div class="test-header">
                    <div class="test-title">📊 进度显示测试</div>
                    <div class="test-status status-pending" id="progress-status">待测试</div>
                </div>
                <div class="test-controls">
                    <button class="btn-test btn-primary" onclick="testProgress()">模拟进度</button>
                    <button class="btn-test btn-warning" onclick="testProgressCancel()">取消进度</button>
                </div>
                <div class="test-result" id="progress-result">等待测试...</div>
            </div>

            <!-- 键盘快捷键测试 -->
            <div class="test-section">
                <div class="test-header">
                    <div class="test-title">⌨️ 键盘快捷键测试</div>
                    <div class="test-status status-pending" id="keyboard-status">待测试</div>
                </div>
                <div class="test-controls">
                    <p>尝试以下快捷键：</p>
                    <ul>
                        <li><kbd>Ctrl+1</kbd> - 切换到分析标签</li>
                        <li><kbd>Ctrl+2</kbd> - 切换到报告标签</li>
                        <li><kbd>Ctrl+3</kbd> - 切换到设置标签</li>
                        <li><kbd>Esc</kbd> - 关闭模态框</li>
                    </ul>
                </div>
                <div class="test-result" id="keyboard-result">等待测试...</div>
            </div>

            <!-- 综合测试 -->
            <div class="test-section">
                <div class="test-header">
                    <div class="test-title">🧪 综合功能测试</div>
                    <div class="test-status status-pending" id="integration-status">待测试</div>
                </div>
                <div class="test-controls">
                    <button class="btn-test btn-success" onclick="runAllTests()">运行所有测试</button>
                    <button class="btn-test btn-danger" onclick="clearAllTests()">清空结果</button>
                </div>
                <div class="test-result" id="integration-result">等待测试...</div>
            </div>
        </div>
    </main>

    <script type="module">
        // 模拟UI管理器（用于测试）
        class TestUIManager {
            constructor() {
                this.notifications = [];
                this.modals = new Map();
                this.activeTab = 'analysis';
                this.init();
            }

            init() {
                this.setupNotificationSystem();
                this.setupModalSystem();
                this.setupKeyboardShortcuts();
            }

            setupNotificationSystem() {
                if (!document.getElementById('notification-container')) {
                    const container = document.createElement('div');
                    container.id = 'notification-container';
                    container.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 10000;
                        pointer-events: none;
                    `;
                    document.body.appendChild(container);
                }
            }

            setupModalSystem() {
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        this.closeAllModals();
                        this.updateTestResult('keyboard', '✅ ESC键关闭模态框测试通过');
                    }
                });
            }

            setupKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.key >= '1' && e.key <= '3') {
                        e.preventDefault();
                        const tabs = ['analysis', 'reports', 'settings'];
                        const tab = tabs[parseInt(e.key) - 1];
                        this.switchTab(tab);
                        this.updateTestResult('keyboard', `✅ Ctrl+${e.key} 切换到 ${tab} 标签测试通过`);
                    }
                });
            }

            showNotification(message, type = 'info', duration = 3000) {
                const notification = document.createElement('div');
                notification.className = `message-toast message-${type}`;
                notification.style.pointerEvents = 'auto';
                
                const icon = this.getNotificationIcon(type);
                notification.innerHTML = `
                    <i class="fas ${icon}"></i>
                    <span>${message}</span>
                    <button class="message-close" onclick="this.parentElement.remove()">×</button>
                `;

                const container = document.getElementById('notification-container');
                container.appendChild(notification);

                if (duration > 0) {
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.style.animation = 'slideOutRight 0.3s ease';
                            setTimeout(() => notification.remove(), 300);
                        }
                    }, duration);
                }

                return notification;
            }

            getNotificationIcon(type) {
                const icons = {
                    success: 'fa-check-circle',
                    error: 'fa-exclamation-circle',
                    warning: 'fa-exclamation-triangle',
                    info: 'fa-info-circle'
                };
                return icons[type] || icons.info;
            }

            showModal(title, content, options = {}) {
                const modalId = `modal-${Date.now()}`;
                const modal = document.createElement('div');
                modal.className = 'modal-overlay';
                modal.id = modalId;
                
                modal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>${title}</h3>
                            <button class="modal-close" onclick="window.testUI.closeModal('${modalId}')">×</button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                        ${options.footer ? `<div class="modal-footer">${options.footer}</div>` : ''}
                    </div>
                `;

                document.body.appendChild(modal);
                this.modals.set(modalId, modal);

                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.closeModal(modalId);
                    }
                });

                return modalId;
            }

            closeModal(modalId) {
                const modal = this.modals.get(modalId);
                if (modal) {
                    modal.remove();
                    this.modals.delete(modalId);
                }
            }

            closeAllModals() {
                this.modals.forEach((modal, modalId) => {
                    this.closeModal(modalId);
                });
            }

            switchTab(tabName) {
                this.activeTab = tabName;
                console.log(`切换到标签页: ${tabName}`);
            }

            updateProgress(current, total, message = '') {
                const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
                console.log(`进度更新: ${percentage}% - ${message}`);
            }

            updateTestResult(testName, result) {
                const resultElement = document.getElementById(`${testName}-result`);
                const statusElement = document.getElementById(`${testName}-status`);
                
                if (resultElement) {
                    resultElement.textContent = result;
                }
                
                if (statusElement) {
                    statusElement.textContent = '测试完成';
                    statusElement.className = 'test-status status-success';
                }
            }
        }

        // 创建测试UI管理器实例
        window.testUI = new TestUIManager();

        // 测试函数
        window.testNotification = function(type) {
            const messages = {
                success: '这是一个成功通知！',
                error: '这是一个错误通知！',
                warning: '这是一个警告通知！',
                info: '这是一个信息通知！'
            };
            
            window.testUI.showNotification(messages[type], type);
            window.testUI.updateTestResult('notification', `✅ ${type} 通知测试通过`);
        };

        window.testModal = function() {
            const modalId = window.testUI.showModal(
                '测试模态框',
                '<p>这是一个测试模态框的内容。</p><p>你可以点击背景或按ESC键关闭它。</p>'
            );
            window.testUI.updateTestResult('modal', `✅ 模态框显示测试通过 (ID: ${modalId})`);
        };

        window.testModalWithFooter = function() {
            const modalId = window.testUI.showModal(
                '带按钮的模态框',
                '<p>这是一个带有底部按钮的模态框。</p>',
                {
                    footer: `
                        <button class="btn-secondary" onclick="window.testUI.closeModal('${Date.now()}')">取消</button>
                        <button class="btn-primary" onclick="window.testUI.closeModal('${Date.now()}')">确定</button>
                    `
                }
            );
            window.testUI.updateTestResult('modal', `✅ 带按钮模态框测试通过 (ID: ${modalId})`);
        };

        window.testTabSwitch = function(tabName) {
            window.testUI.switchTab(tabName);
            window.testUI.updateTestResult('tabs', `✅ 切换到 ${tabName} 标签测试通过`);
        };

        window.testProgress = function() {
            let current = 0;
            const total = 10;
            
            const interval = setInterval(() => {
                current++;
                window.testUI.updateProgress(current, total, `处理第 ${current} 项`);
                
                if (current >= total) {
                    clearInterval(interval);
                    window.testUI.updateTestResult('progress', '✅ 进度显示测试完成');
                }
            }, 200);
        };

        window.testProgressCancel = function() {
            window.testUI.updateTestResult('progress', '✅ 进度取消测试通过');
        };

        window.runAllTests = function() {
            window.testNotification('success');
            setTimeout(() => window.testModal(), 500);
            setTimeout(() => window.testTabSwitch('reports'), 1000);
            setTimeout(() => window.testProgress(), 1500);
            
            window.testUI.updateTestResult('integration', '✅ 所有测试已运行');
        };

        window.clearAllTests = function() {
            const results = document.querySelectorAll('.test-result');
            const statuses = document.querySelectorAll('.test-status');
            
            results.forEach(result => result.textContent = '等待测试...');
            statuses.forEach(status => {
                status.textContent = '待测试';
                status.className = 'test-status status-pending';
            });
        };
    </script>
</body>
</html>
