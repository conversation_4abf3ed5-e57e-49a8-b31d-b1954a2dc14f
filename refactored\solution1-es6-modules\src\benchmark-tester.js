/* === Naming Conventions Header (auto-injected 2025-08-14) ===
    Benchmark tester constants: BENCH_* prefix.
*/
/**
 * ==================== GoMyHire 对话分析系统 - 性能基准测试器 ====================
 * @SERVICE 性能基准测试工具
 * 实现新系统与原系统的性能对比测试，确保性能不低于原系统
 */

import { generateUniqueId, safeExecute } from './utils.js';
import { getPerformanceTester } from './performance-tester.js';

// ==================== 性能基准测试器类 ====================
/**
 * 性能基准测试器类 - 负责新旧系统性能对比测试
 * @COMPONENT 性能基准测试器
 */
export class BenchmarkTester {
    constructor() {
        this.performanceTester = null;
        this.benchmarkResults = new Map();
        this.isRunning = false;
        this.baselineMetrics = null;
        this.benchmarkSuites = new Map();

        this.initialize();
        console.log('📊 BenchmarkTester 初始化完成');
    }

    /**
     * 初始化基准测试器
     * @INIT 基准测试器初始化方法
     */
    initialize() {
        this.performanceTester = getPerformanceTester();
        this.setupBenchmarkSuites();
        this.loadBaselineMetrics();
    }

    /**
     * 设置基准测试套件
     * @SERVICE 基准测试套件设置方法
     */
    setupBenchmarkSuites() {
        // 系统启动性能测试
        this.benchmarkSuites.set('systemStartup', {
            name: '系统启动性能',
            description: '测试系统初始化和模块加载时间',
            baseline: {
                totalTime: 3000, // 原系统约3秒启动
                moduleCount: 1, // 原系统是单体文件
                memoryUsage: 30 // 原系统约30MB内存
            },
            testMethod: 'benchmarkSystemStartup'
        });

        // 文件处理性能测试
        this.benchmarkSuites.set('fileProcessing', {
            name: '文件处理性能',
            description: '测试文件上传和解析处理速度',
            baseline: {
                throughput: 50, // 原系统约50文件/秒
                averageTime: 200, // 平均200ms/文件
                memoryEfficiency: 0.8 // 内存效率80%
            },
            testMethod: 'benchmarkFileProcessing'
        });

        // UI响应性能测试
        this.benchmarkSuites.set('uiResponse', {
            name: 'UI响应性能',
            description: '测试用户界面交互响应时间',
            baseline: {
                clickResponse: 50, // 点击响应50ms
                chartUpdate: 300, // 图表更新300ms
                tabSwitch: 100 // 标签页切换100ms
            },
            testMethod: 'benchmarkUIResponse'
        });

        // 内存使用效率测试
        this.benchmarkSuites.set('memoryEfficiency', {
            name: '内存使用效率',
            description: '测试内存占用和垃圾回收效率',
            baseline: {
                initialMemory: 25, // 初始内存25MB
                peakMemory: 80, // 峰值内存80MB
                gcEfficiency: 0.9 // GC效率90%
            },
            testMethod: 'benchmarkMemoryEfficiency'
        });

        // 并发处理性能测试
        this.benchmarkSuites.set('concurrency', {
            name: '并发处理性能',
            description: '测试多任务并发处理能力',
            baseline: {
                maxConcurrency: 50, // 最大并发50
                taskThroughput: 100, // 任务吞吐量100/秒
                errorRate: 0.05 // 错误率5%
            },
            testMethod: 'benchmarkConcurrency'
        });

        console.log(`📊 设置了 ${this.benchmarkSuites.size} 个基准测试套件`);
    }

    /**
     * 加载基线指标
     * @SERVICE 基线指标加载方法
     */
    loadBaselineMetrics() {
        // 从localStorage加载之前的基线数据
        try {
            const stored = localStorage.getItem('benchmark_baseline');
            if (stored) {
                this.baselineMetrics = JSON.parse(stored);
                console.log('📊 已加载基线指标');
            }
        } catch (error) {
            console.warn('加载基线指标失败:', error);
        }

        // 如果没有基线数据，使用默认值
        if (!this.baselineMetrics) {
            this.baselineMetrics = {
                systemStartup: { totalTime: 3000, moduleCount: 1, memoryUsage: 30 },
                fileProcessing: { throughput: 50, averageTime: 200, memoryEfficiency: 0.8 },
                uiResponse: { clickResponse: 50, chartUpdate: 300, tabSwitch: 100 },
                memoryEfficiency: { initialMemory: 25, peakMemory: 80, gcEfficiency: 0.9 },
                concurrency: { maxConcurrency: 50, taskThroughput: 100, errorRate: 0.05 }
            };
        }
    }

    /**
     * 运行完整基准测试
     * @SERVICE 完整基准测试运行方法
     * @returns {Promise<Object>} 基准测试结果
     */
    async runFullBenchmarkTest() {
        if (this.isRunning) {
            console.warn('基准测试正在运行中');
            return null;
        }

        this.isRunning = true;
        const startTime = performance.now();

        try {
            console.log('🚀 开始性能基准测试...');

            const results = {
                summary: {
                    totalSuites: 0,
                    passedSuites: 0,
                    failedSuites: 0,
                    overallScore: 0,
                    performanceImprovement: 0
                },
                suiteResults: {},
                comparison: {},
                recommendations: []
            };

            // 运行所有基准测试套件
            for (const [suiteName, suite] of this.benchmarkSuites) {
                console.log(`📦 运行基准测试: ${suite.name}`);
                
                const suiteResult = await this.runBenchmarkSuite(suiteName, suite);
                results.suiteResults[suiteName] = suiteResult;
                
                // 更新统计
                results.summary.totalSuites++;
                if (suiteResult.passed) {
                    results.summary.passedSuites++;
                } else {
                    results.summary.failedSuites++;
                }
            }

            // 计算总体评分
            results.summary.overallScore = this.calculateOverallScore(results.suiteResults);
            
            // 计算性能改进
            results.summary.performanceImprovement = this.calculatePerformanceImprovement(results.suiteResults);
            
            // 生成对比分析
            results.comparison = this.generateComparisonAnalysis(results.suiteResults);
            
            // 生成建议
            results.recommendations = this.generateBenchmarkRecommendations(results);

            const endTime = performance.now();
            const totalTime = endTime - startTime;

            console.log(`✅ 基准测试完成，总体评分: ${results.summary.overallScore}%`);
            
            // 保存测试结果
            this.benchmarkResults.set(Date.now(), {
                ...results,
                totalTime: totalTime,
                timestamp: new Date().toISOString()
            });

            return {
                success: true,
                results: results,
                totalTime: totalTime,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ 基准测试失败:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 运行基准测试套件
     * @SERVICE 基准测试套件运行方法
     * @param {string} suiteName - 套件名称
     * @param {Object} suite - 套件配置
     * @returns {Promise<Object>} 套件测试结果
     */
    async runBenchmarkSuite(suiteName, suite) {
        const startTime = performance.now();
        
        try {
            // 运行具体测试
            const testMethod = this[suite.testMethod];
            if (typeof testMethod !== 'function') {
                throw new Error(`测试方法不存在: ${suite.testMethod}`);
            }

            const testResult = await testMethod.call(this, suite);
            const endTime = performance.now();

            // 与基线对比
            const baseline = this.baselineMetrics[suiteName] || suite.baseline;
            const comparison = this.compareWithBaseline(testResult, baseline);

            return {
                suiteName: suiteName,
                passed: comparison.overallImprovement >= 0, // 性能不低于基线
                testResult: testResult,
                baseline: baseline,
                comparison: comparison,
                duration: endTime - startTime
            };

        } catch (error) {
            const endTime = performance.now();
            
            return {
                suiteName: suiteName,
                passed: false,
                error: error.message,
                duration: endTime - startTime
            };
        }
    }

    // ==================== 具体基准测试方法 ====================

    /**
     * 系统启动性能基准测试
     * @TEST 系统启动性能基准测试
     */
    async benchmarkSystemStartup(suite) {
        const results = {
            totalTime: 0,
            moduleCount: 0,
            memoryUsage: 0,
            details: {}
        };

        try {
            const startTime = performance.now();
            
            // 模拟系统启动过程
            results.details.moduleLoadStart = performance.now();
            
            // 检查已加载模块数量
            results.moduleCount = window.ModuleExports ? Object.keys(window.ModuleExports).length : 0;
            
            results.details.moduleLoadEnd = performance.now();
            results.details.moduleLoadTime = results.details.moduleLoadEnd - results.details.moduleLoadStart;
            
            // 检查内存使用
            if (performance.memory) {
                results.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            }
            
            // 模拟应用初始化
            results.details.appInitStart = performance.now();
            
            // 检查主应用是否初始化
            const mainAppInitialized = !!(window.mainApp && window.mainApp.isInitialized);
            results.details.mainAppInitialized = mainAppInitialized;
            
            results.details.appInitEnd = performance.now();
            results.details.appInitTime = results.details.appInitEnd - results.details.appInitStart;
            
            const endTime = performance.now();
            results.totalTime = endTime - startTime;

            console.log(`📊 系统启动测试: ${Math.round(results.totalTime)}ms, ${results.moduleCount}个模块, ${results.memoryUsage}MB`);

        } catch (error) {
            results.error = error.message;
        }

        return results;
    }

    /**
     * 文件处理性能基准测试
     * @TEST 文件处理性能基准测试
     */
    async benchmarkFileProcessing(suite) {
        const results = {
            throughput: 0,
            averageTime: 0,
            memoryEfficiency: 0,
            details: {}
        };

        try {
            const testFileCount = 100;
            const startTime = performance.now();
            const startMemory = this.getMemoryUsage();

            // 模拟文件处理
            const processingTimes = [];
            for (let i = 0; i < testFileCount; i++) {
                const fileStartTime = performance.now();
                
                // 模拟文件处理逻辑
                await this.simulateFileProcessing(i);
                
                const fileEndTime = performance.now();
                processingTimes.push(fileEndTime - fileStartTime);
            }

            const endTime = performance.now();
            const endMemory = this.getMemoryUsage();
            
            const totalTime = endTime - startTime;
            results.throughput = (testFileCount / totalTime) * 1000; // files per second
            results.averageTime = processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length;
            
            const memoryIncrease = endMemory - startMemory;
            results.memoryEfficiency = memoryIncrease > 0 ? Math.min(1, 10 / memoryIncrease) : 1;
            
            results.details = {
                testFileCount: testFileCount,
                totalTime: totalTime,
                memoryIncrease: memoryIncrease,
                minProcessingTime: Math.min(...processingTimes),
                maxProcessingTime: Math.max(...processingTimes)
            };

            console.log(`📊 文件处理测试: ${Math.round(results.throughput)}文件/秒, 平均${Math.round(results.averageTime)}ms`);

        } catch (error) {
            results.error = error.message;
        }

        return results;
    }

    /**
     * UI响应性能基准测试
     * @TEST UI响应性能基准测试
     */
    async benchmarkUIResponse(suite) {
        const results = {
            clickResponse: 0,
            chartUpdate: 0,
            tabSwitch: 0,
            details: {}
        };

        try {
            // 测试点击响应
            const clickTimes = [];
            for (let i = 0; i < 10; i++) {
                const startTime = performance.now();
                await this.simulateClick();
                const endTime = performance.now();
                clickTimes.push(endTime - startTime);
            }
            results.clickResponse = clickTimes.reduce((a, b) => a + b, 0) / clickTimes.length;

            // 测试图表更新
            const chartTimes = [];
            for (let i = 0; i < 5; i++) {
                const startTime = performance.now();
                await this.simulateChartUpdate();
                const endTime = performance.now();
                chartTimes.push(endTime - startTime);
            }
            results.chartUpdate = chartTimes.reduce((a, b) => a + b, 0) / chartTimes.length;

            // 测试标签页切换
            const tabTimes = [];
            for (let i = 0; i < 5; i++) {
                const startTime = performance.now();
                await this.simulateTabSwitch();
                const endTime = performance.now();
                tabTimes.push(endTime - startTime);
            }
            results.tabSwitch = tabTimes.reduce((a, b) => a + b, 0) / tabTimes.length;

            results.details = {
                clickTests: clickTimes.length,
                chartTests: chartTimes.length,
                tabTests: tabTimes.length
            };

            console.log(`📊 UI响应测试: 点击${Math.round(results.clickResponse)}ms, 图表${Math.round(results.chartUpdate)}ms, 标签页${Math.round(results.tabSwitch)}ms`);

        } catch (error) {
            results.error = error.message;
        }

        return results;
    }

    /**
     * 内存使用效率基准测试
     * @TEST 内存使用效率基准测试
     */
    async benchmarkMemoryEfficiency(suite) {
        const results = {
            initialMemory: 0,
            peakMemory: 0,
            gcEfficiency: 0,
            details: {}
        };

        try {
            results.initialMemory = this.getMemoryUsage();
            
            // 创建大量对象测试内存使用
            const testData = [];
            for (let i = 0; i < 10000; i++) {
                testData.push({
                    id: generateUniqueId('test'),
                    data: new Array(100).fill(Math.random()),
                    timestamp: Date.now()
                });
            }
            
            results.peakMemory = this.getMemoryUsage();
            
            // 清理数据
            testData.length = 0;
            
            // 强制垃圾回收（如果可能）
            if (window.gc) {
                window.gc();
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
            const finalMemory = this.getMemoryUsage();
            
            const memoryRecovered = results.peakMemory - finalMemory;
            const memoryIncrease = results.peakMemory - results.initialMemory;
            results.gcEfficiency = memoryIncrease > 0 ? memoryRecovered / memoryIncrease : 1;
            
            results.details = {
                memoryIncrease: memoryIncrease,
                memoryRecovered: memoryRecovered,
                finalMemory: finalMemory
            };

            console.log(`📊 内存效率测试: 初始${results.initialMemory}MB, 峰值${results.peakMemory}MB, GC效率${Math.round(results.gcEfficiency * 100)}%`);

        } catch (error) {
            results.error = error.message;
        }

        return results;
    }

    /**
     * 并发处理性能基准测试
     * @TEST 并发处理性能基准测试
     */
    async benchmarkConcurrency(suite) {
        const results = {
            maxConcurrency: 0,
            taskThroughput: 0,
            errorRate: 0,
            details: {}
        };

        try {
            const taskCount = 200;
            const startTime = performance.now();
            
            // 创建并发任务
            const tasks = [];
            for (let i = 0; i < taskCount; i++) {
                tasks.push(this.simulateConcurrentTask(i));
            }
            
            // 执行并发任务
            const taskResults = await Promise.allSettled(tasks);
            
            const endTime = performance.now();
            const totalTime = endTime - startTime;
            
            const successful = taskResults.filter(r => r.status === 'fulfilled').length;
            const failed = taskResults.filter(r => r.status === 'rejected').length;
            
            results.maxConcurrency = taskCount; // 实际支持的最大并发数
            results.taskThroughput = (successful / totalTime) * 1000; // tasks per second
            results.errorRate = failed / taskCount;
            
            results.details = {
                taskCount: taskCount,
                successful: successful,
                failed: failed,
                totalTime: totalTime
            };

            console.log(`📊 并发测试: ${taskCount}并发, ${Math.round(results.taskThroughput)}任务/秒, 错误率${Math.round(results.errorRate * 100)}%`);

        } catch (error) {
            results.error = error.message;
        }

        return results;
    }

    // ==================== 辅助方法 ====================

    /**
     * 模拟文件处理
     * @UTIL 文件处理模拟工具
     */
    async simulateFileProcessing(index) {
        const processingTime = Math.random() * 50 + 10; // 10-60ms
        return new Promise(resolve => setTimeout(resolve, processingTime));
    }

    /**
     * 模拟点击操作
     * @UTIL 点击操作模拟工具
     */
    async simulateClick() {
        // 模拟DOM操作
        const testElement = document.createElement('button');
        document.body.appendChild(testElement);
        testElement.click();
        document.body.removeChild(testElement);
        return Promise.resolve();
    }

    /**
     * 模拟图表更新
     * @UTIL 图表更新模拟工具
     */
    async simulateChartUpdate() {
        // 模拟图表渲染
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 300;
        const ctx = canvas.getContext('2d');
        
        // 绘制一些图形
        ctx.fillStyle = 'blue';
        ctx.fillRect(0, 0, 100, 100);
        
        return Promise.resolve();
    }

    /**
     * 模拟标签页切换
     * @UTIL 标签页切换模拟工具
     */
    async simulateTabSwitch() {
        // 模拟标签页切换操作
        const tabContainer = document.createElement('div');
        tabContainer.innerHTML = '<div class="tab active">Tab 1</div><div class="tab">Tab 2</div>';
        
        const tabs = tabContainer.querySelectorAll('.tab');
        tabs[0].classList.remove('active');
        tabs[1].classList.add('active');
        
        return Promise.resolve();
    }

    /**
     * 模拟并发任务
     * @UTIL 并发任务模拟工具
     */
    async simulateConcurrentTask(index) {
        const taskTime = Math.random() * 100 + 50; // 50-150ms
        
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (Math.random() > 0.95) { // 5% 失败率
                    reject(new Error(`任务 ${index} 失败`));
                } else {
                    resolve({ taskIndex: index, result: 'success' });
                }
            }, taskTime);
        });
    }

    /**
     * 获取内存使用情况
     * @UTIL 内存使用获取工具
     */
    getMemoryUsage() {
        if (performance.memory) {
            return Math.round(performance.memory.usedJSHeapSize / 1024 / 1024 * 100) / 100;
        }
        return 0;
    }

    /**
     * 与基线对比
     * @UTIL 基线对比工具
     */
    compareWithBaseline(testResult, baseline) {
        const comparison = {
            improvements: {},
            regressions: {},
            overallImprovement: 0
        };

        let totalImprovement = 0;
        let comparisonCount = 0;

        for (const [key, baselineValue] of Object.entries(baseline)) {
            if (testResult[key] !== undefined) {
                const testValue = testResult[key];
                let improvement = 0;

                // 根据指标类型计算改进
                if (key.includes('Time') || key.includes('Response')) {
                    // 时间类指标：越小越好
                    improvement = (baselineValue - testValue) / baselineValue;
                } else if (key.includes('throughput') || key.includes('Efficiency')) {
                    // 吞吐量和效率类指标：越大越好
                    improvement = (testValue - baselineValue) / baselineValue;
                } else if (key.includes('errorRate')) {
                    // 错误率：越小越好
                    improvement = (baselineValue - testValue) / baselineValue;
                } else {
                    // 其他指标：根据具体情况
                    improvement = (testValue - baselineValue) / baselineValue;
                }

                if (improvement > 0) {
                    comparison.improvements[key] = improvement;
                } else {
                    comparison.regressions[key] = Math.abs(improvement);
                }

                totalImprovement += improvement;
                comparisonCount++;
            }
        }

        comparison.overallImprovement = comparisonCount > 0 ? totalImprovement / comparisonCount : 0;
        return comparison;
    }

    /**
     * 计算总体评分
     * @UTIL 总体评分计算工具
     */
    calculateOverallScore(suiteResults) {
        const scores = [];
        
        for (const result of Object.values(suiteResults)) {
            if (result.comparison && result.comparison.overallImprovement !== undefined) {
                // 将改进率转换为评分（0-100）
                const score = Math.max(0, Math.min(100, 50 + result.comparison.overallImprovement * 50));
                scores.push(score);
            }
        }

        return scores.length > 0 ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : 0;
    }

    /**
     * 计算性能改进
     * @UTIL 性能改进计算工具
     */
    calculatePerformanceImprovement(suiteResults) {
        const improvements = [];
        
        for (const result of Object.values(suiteResults)) {
            if (result.comparison && result.comparison.overallImprovement !== undefined) {
                improvements.push(result.comparison.overallImprovement);
            }
        }

        return improvements.length > 0 ? 
            Math.round(improvements.reduce((a, b) => a + b, 0) / improvements.length * 100) : 0;
    }

    /**
     * 生成对比分析
     * @UTIL 对比分析生成工具
     */
    generateComparisonAnalysis(suiteResults) {
        const analysis = {
            improvements: [],
            regressions: [],
            summary: {}
        };

        for (const [suiteName, result] of Object.entries(suiteResults)) {
            if (result.comparison) {
                for (const [metric, improvement] of Object.entries(result.comparison.improvements)) {
                    analysis.improvements.push({
                        suite: suiteName,
                        metric: metric,
                        improvement: Math.round(improvement * 100)
                    });
                }

                for (const [metric, regression] of Object.entries(result.comparison.regressions)) {
                    analysis.regressions.push({
                        suite: suiteName,
                        metric: metric,
                        regression: Math.round(regression * 100)
                    });
                }
            }
        }

        analysis.summary = {
            totalImprovements: analysis.improvements.length,
            totalRegressions: analysis.regressions.length,
            netImprovement: analysis.improvements.length - analysis.regressions.length
        };

        return analysis;
    }

    /**
     * 生成基准测试建议
     * @UTIL 基准测试建议生成工具
     */
    generateBenchmarkRecommendations(results) {
        const recommendations = [];
        
        if (results.summary.overallScore < 60) {
            recommendations.push('系统整体性能低于基线，需要进行优化');
        }

        if (results.summary.performanceImprovement < 0) {
            recommendations.push('新系统性能低于原系统，建议检查性能瓶颈');
        }

        if (results.comparison.regressions.length > results.comparison.improvements.length) {
            recommendations.push('性能退化项目多于改进项目，需要重点关注');
        }

        // 针对具体套件的建议
        for (const [suiteName, result] of Object.entries(results.suiteResults)) {
            if (!result.passed) {
                switch (suiteName) {
                    case 'systemStartup':
                        recommendations.push('系统启动性能需要优化，考虑实现更好的模块懒加载');
                        break;
                    case 'fileProcessing':
                        recommendations.push('文件处理性能需要优化，考虑优化并发策略');
                        break;
                    case 'uiResponse':
                        recommendations.push('UI响应性能需要优化，考虑使用虚拟化或防抖技术');
                        break;
                    case 'memoryEfficiency':
                        recommendations.push('内存使用效率需要优化，检查内存泄漏');
                        break;
                    case 'concurrency':
                        recommendations.push('并发处理性能需要优化，调整并发参数');
                        break;
                }
            }
        }

        if (recommendations.length === 0) {
            recommendations.push('性能基准测试通过，系统性能表现良好');
        }

        return recommendations;
    }

    /**
     * 保存基线指标
     * @SERVICE 基线指标保存方法
     */
    saveBaselineMetrics(metrics) {
        try {
            this.baselineMetrics = metrics;
            localStorage.setItem('benchmark_baseline', JSON.stringify(metrics));
            console.log('📊 基线指标已保存');
        } catch (error) {
            console.error('保存基线指标失败:', error);
        }
    }

    /**
     * 获取基准测试历史
     * @SERVICE 基准测试历史获取方法
     */
    getBenchmarkHistory() {
        return Array.from(this.benchmarkResults.entries()).map(([timestamp, results]) => ({
            timestamp: new Date(timestamp).toISOString(),
            results: results
        }));
    }

    /**
     * 清除基准测试历史
     * @SERVICE 基准测试历史清除方法
     */
    clearBenchmarkHistory() {
        this.benchmarkResults.clear();
        console.log('🧹 基准测试历史已清除');
    }

    /**
     * 销毁基准测试器
     * @LIFECYCLE 基准测试器销毁方法
     */
    destroy() {
        this.clearBenchmarkHistory();
        this.benchmarkSuites.clear();
        this.performanceTester = null;
        this.baselineMetrics = null;
        console.log('🗑️ BenchmarkTester 已销毁');
    }
}

// ==================== 全局实例 ====================
let globalBenchmarkTester = null;

/**
 * 获取全局基准测试器实例
 * @SERVICE 全局基准测试器获取函数
 * @returns {BenchmarkTester} 基准测试器实例
 */
export function getBenchmarkTester() {
    if (!globalBenchmarkTester) {
        globalBenchmarkTester = new BenchmarkTester();
    }
    return globalBenchmarkTester;
}

// ==================== 便捷函数 ====================

/**
 * 运行性能基准测试
 * @SERVICE 性能基准测试运行函数
 * @returns {Promise<Object>} 基准测试结果
 */
export async function runBenchmarkTest() {
    return await getBenchmarkTester().runFullBenchmarkTest();
}

/**
 * 快速性能对比
 * @SERVICE 快速性能对比函数
 * @returns {Object} 性能对比结果
 */
export function quickPerformanceComparison() {
    const tester = getBenchmarkTester();
    
    return {
        currentMetrics: {
            memoryUsage: tester.getMemoryUsage(),
            moduleCount: window.ModuleExports ? Object.keys(window.ModuleExports).length : 0,
            timestamp: Date.now()
        },
        baselineMetrics: tester.baselineMetrics,
        hasBaseline: !!tester.baselineMetrics
    };
}
