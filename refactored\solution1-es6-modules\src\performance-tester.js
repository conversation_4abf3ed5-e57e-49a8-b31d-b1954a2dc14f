/* === Naming Conventions Header (auto-injected 2025-08-14) ===
    Performance test constants: PERF_TEST_* prefix.
*/
/**
 * ==================== GoMyHire 对话分析系统 - 性能测试器 ====================
 * @SERVICE 系统性能测试和调优工具
 * 实现模块加载时间测试、内存使用监控、并发处理效率测试等功能
 */

import { generateUniqueId, safeExecute } from './utils.js';
import { getPerformanceMonitor } from './performance-monitor.js';

// ==================== 性能测试器类 ====================
/**
 * 性能测试器类 - 负责系统性能测试和调优
 * @COMPONENT 性能测试器
 */
export class PerformanceTester {
    constructor() {
        this.performanceMonitor = null;
        this.testResults = new Map();
        this.benchmarks = new Map();
        this.isRunning = false;
        this.testStartTime = null;
        this.testEndTime = null;

        this.initialize();
        console.log('🔬 PerformanceTester 初始化完成');
    }

    /**
     * 初始化性能测试器
     * @INIT 性能测试器初始化方法
     */
    initialize() {
        this.performanceMonitor = getPerformanceMonitor();
        this.setupBenchmarks();
    }

    /**
     * 设置基准测试
     * @SERVICE 基准测试设置方法
     */
    setupBenchmarks() {
        // 模块加载基准
        this.benchmarks.set('moduleLoading', {
            name: '模块加载性能',
            target: 2000, // 目标：2秒内完成所有模块加载
            unit: 'ms',
            description: '测试所有模块的加载时间'
        });

        // 内存使用基准
        this.benchmarks.set('memoryUsage', {
            name: '内存使用',
            target: 50, // 目标：不超过50MB
            unit: 'MB',
            description: '测试系统内存占用'
        });

        // 文件处理基准
        this.benchmarks.set('fileProcessing', {
            name: '文件处理性能',
            target: 100, // 目标：每秒处理100个文件
            unit: 'files/sec',
            description: '测试文件处理并发能力'
        });

        // UI响应基准
        this.benchmarks.set('uiResponse', {
            name: 'UI响应时间',
            target: 100, // 目标：100ms内响应
            unit: 'ms',
            description: '测试UI交互响应时间'
        });

        console.log(`📊 设置了 ${this.benchmarks.size} 个性能基准`);
    }

    /**
     * 运行完整性能测试
     * @SERVICE 完整性能测试运行方法
     * @returns {Promise<Object>} 测试结果
     */
    async runFullPerformanceTest() {
        if (this.isRunning) {
            console.warn('性能测试正在运行中');
            return null;
        }

        this.isRunning = true;
        this.testStartTime = performance.now();
        
        try {
            console.log('🚀 开始完整性能测试...');

            const results = {
                moduleLoading: await this.testModuleLoadingPerformance(),
                memoryUsage: await this.testMemoryUsage(),
                fileProcessing: await this.testFileProcessingPerformance(),
                uiResponse: await this.testUIResponseTime(),
                overall: null
            };

            // 计算总体评分
            results.overall = this.calculateOverallScore(results);

            this.testEndTime = performance.now();
            const totalTime = this.testEndTime - this.testStartTime;

            console.log(`✅ 性能测试完成，总耗时: ${Math.round(totalTime)}ms`);
            
            // 保存测试结果
            this.testResults.set(Date.now(), results);

            return {
                success: true,
                results: results,
                totalTime: totalTime,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ 性能测试失败:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 测试模块加载性能
     * @SERVICE 模块加载性能测试方法
     * @returns {Promise<Object>} 测试结果
     */
    async testModuleLoadingPerformance() {
        console.log('📦 测试模块加载性能...');
        
        const startTime = performance.now();
        const startMemory = this.getMemoryUsage();

        try {
            // 清除模块缓存（如果可能）
            if (window.ModuleExports) {
                const moduleCount = Object.keys(window.ModuleExports).length;
                console.log(`当前已加载 ${moduleCount} 个模块`);
            }

            // 模拟重新加载关键模块
            const criticalModules = [
                'utils.js',
                'constants.js',
                'service-container.js',
                'event-bus.js',
                'notification.js'
            ];

            const loadTimes = [];
            for (const moduleName of criticalModules) {
                const moduleStartTime = performance.now();
                
                try {
                    // 模拟模块加载
                    await new Promise(resolve => setTimeout(resolve, Math.random() * 50));
                    
                    const moduleEndTime = performance.now();
                    const loadTime = moduleEndTime - moduleStartTime;
                    loadTimes.push(loadTime);
                    
                    console.log(`  ${moduleName}: ${Math.round(loadTime)}ms`);
                } catch (error) {
                    console.warn(`  ${moduleName}: 加载失败`);
                    loadTimes.push(1000); // 失败时记录为1秒
                }
            }

            const endTime = performance.now();
            const endMemory = this.getMemoryUsage();
            
            const totalTime = endTime - startTime;
            const averageTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length;
            const memoryIncrease = endMemory - startMemory;

            const benchmark = this.benchmarks.get('moduleLoading');
            const score = Math.max(0, 100 - (totalTime / benchmark.target) * 100);

            return {
                totalTime: totalTime,
                averageTime: averageTime,
                moduleCount: criticalModules.length,
                memoryIncrease: memoryIncrease,
                score: Math.round(score),
                passed: totalTime <= benchmark.target,
                benchmark: benchmark
            };

        } catch (error) {
            return {
                error: error.message,
                score: 0,
                passed: false
            };
        }
    }

    /**
     * 测试内存使用
     * @SERVICE 内存使用测试方法
     * @returns {Promise<Object>} 测试结果
     */
    async testMemoryUsage() {
        console.log('💾 测试内存使用...');

        try {
            const initialMemory = this.getMemoryUsage();
            
            // 模拟内存密集操作
            const testData = [];
            for (let i = 0; i < 10000; i++) {
                testData.push({
                    id: generateUniqueId('test'),
                    data: new Array(100).fill(Math.random()),
                    timestamp: Date.now()
                });
            }

            const peakMemory = this.getMemoryUsage();
            
            // 清理测试数据
            testData.length = 0;
            
            // 强制垃圾回收（如果可能）
            if (window.gc) {
                window.gc();
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
            const finalMemory = this.getMemoryUsage();

            const memoryIncrease = peakMemory - initialMemory;
            const memoryRecovered = peakMemory - finalMemory;
            
            const benchmark = this.benchmarks.get('memoryUsage');
            const score = Math.max(0, 100 - (memoryIncrease / benchmark.target) * 100);

            return {
                initialMemory: initialMemory,
                peakMemory: peakMemory,
                finalMemory: finalMemory,
                memoryIncrease: memoryIncrease,
                memoryRecovered: memoryRecovered,
                score: Math.round(score),
                passed: memoryIncrease <= benchmark.target,
                benchmark: benchmark
            };

        } catch (error) {
            return {
                error: error.message,
                score: 0,
                passed: false
            };
        }
    }

    /**
     * 测试文件处理性能
     * @SERVICE 文件处理性能测试方法
     * @returns {Promise<Object>} 测试结果
     */
    async testFileProcessingPerformance() {
        console.log('📄 测试文件处理性能...');

        try {
            const fileCount = 100;
            const startTime = performance.now();

            // 模拟文件处理
            const processingTimes = [];
            const concurrentTasks = [];

            for (let i = 0; i < fileCount; i++) {
                const task = this.simulateFileProcessing(i);
                concurrentTasks.push(task);
            }

            const results = await Promise.allSettled(concurrentTasks);
            const endTime = performance.now();

            const totalTime = endTime - startTime;
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;
            const throughput = (successful / totalTime) * 1000; // files per second

            const benchmark = this.benchmarks.get('fileProcessing');
            const score = Math.min(100, (throughput / benchmark.target) * 100);

            return {
                totalTime: totalTime,
                fileCount: fileCount,
                successful: successful,
                failed: failed,
                throughput: throughput,
                score: Math.round(score),
                passed: throughput >= benchmark.target,
                benchmark: benchmark
            };

        } catch (error) {
            return {
                error: error.message,
                score: 0,
                passed: false
            };
        }
    }

    /**
     * 测试UI响应时间
     * @SERVICE UI响应时间测试方法
     * @returns {Promise<Object>} 测试结果
     */
    async testUIResponseTime() {
        console.log('🖱️ 测试UI响应时间...');

        try {
            const responseTimes = [];
            const testCount = 10;

            for (let i = 0; i < testCount; i++) {
                const startTime = performance.now();
                
                // 模拟UI操作
                await this.simulateUIInteraction();
                
                const endTime = performance.now();
                const responseTime = endTime - startTime;
                responseTimes.push(responseTime);
            }

            const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
            const maxResponseTime = Math.max(...responseTimes);
            const minResponseTime = Math.min(...responseTimes);

            const benchmark = this.benchmarks.get('uiResponse');
            const score = Math.max(0, 100 - (averageResponseTime / benchmark.target) * 100);

            return {
                averageResponseTime: averageResponseTime,
                maxResponseTime: maxResponseTime,
                minResponseTime: minResponseTime,
                testCount: testCount,
                score: Math.round(score),
                passed: averageResponseTime <= benchmark.target,
                benchmark: benchmark
            };

        } catch (error) {
            return {
                error: error.message,
                score: 0,
                passed: false
            };
        }
    }

    /**
     * 模拟文件处理
     * @UTIL 文件处理模拟工具
     * @param {number} fileIndex - 文件索引
     * @returns {Promise} 处理结果
     */
    async simulateFileProcessing(fileIndex) {
        const processingTime = Math.random() * 50 + 10; // 10-60ms
        
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (Math.random() > 0.95) { // 5% 失败率
                    reject(new Error(`文件 ${fileIndex} 处理失败`));
                } else {
                    resolve({
                        fileIndex: fileIndex,
                        processingTime: processingTime,
                        result: 'success'
                    });
                }
            }, processingTime);
        });
    }

    /**
     * 模拟UI交互
     * @UTIL UI交互模拟工具
     * @returns {Promise} 交互结果
     */
    async simulateUIInteraction() {
        // 模拟DOM操作
        const testElement = document.createElement('div');
        testElement.innerHTML = '<span>测试内容</span>';
        document.body.appendChild(testElement);
        
        // 模拟样式计算
        const computedStyle = window.getComputedStyle(testElement);
        const height = computedStyle.height;
        
        // 清理
        document.body.removeChild(testElement);
        
        return { height };
    }

    /**
     * 获取内存使用情况
     * @UTIL 内存使用获取工具
     * @returns {number} 内存使用量（MB）
     */
    getMemoryUsage() {
        if (performance.memory) {
            return Math.round(performance.memory.usedJSHeapSize / 1024 / 1024 * 100) / 100;
        }
        return 0;
    }

    /**
     * 计算总体评分
     * @UTIL 总体评分计算工具
     * @param {Object} results - 测试结果
     * @returns {Object} 总体评分
     */
    calculateOverallScore(results) {
        const scores = [];
        const weights = {
            moduleLoading: 0.3,
            memoryUsage: 0.2,
            fileProcessing: 0.3,
            uiResponse: 0.2
        };

        let totalWeight = 0;
        let weightedScore = 0;

        for (const [testName, result] of Object.entries(results)) {
            if (result && typeof result.score === 'number' && weights[testName]) {
                weightedScore += result.score * weights[testName];
                totalWeight += weights[testName];
                scores.push(result.score);
            }
        }

        const overallScore = totalWeight > 0 ? Math.round(weightedScore / totalWeight) : 0;
        const allPassed = Object.values(results).every(r => r && r.passed);

        let grade = 'F';
        if (overallScore >= 90) grade = 'A';
        else if (overallScore >= 80) grade = 'B';
        else if (overallScore >= 70) grade = 'C';
        else if (overallScore >= 60) grade = 'D';

        return {
            score: overallScore,
            grade: grade,
            allTestsPassed: allPassed,
            individualScores: scores,
            recommendation: this.getPerformanceRecommendation(overallScore, results)
        };
    }

    /**
     * 获取性能建议
     * @UTIL 性能建议获取工具
     * @param {number} score - 总体评分
     * @param {Object} results - 测试结果
     * @returns {Array} 建议列表
     */
    getPerformanceRecommendation(score, results) {
        const recommendations = [];

        if (score < 70) {
            recommendations.push('系统整体性能需要优化');
        }

        if (results.moduleLoading && results.moduleLoading.score < 70) {
            recommendations.push('考虑实现模块懒加载和代码分割');
        }

        if (results.memoryUsage && results.memoryUsage.score < 70) {
            recommendations.push('优化内存使用，检查内存泄漏');
        }

        if (results.fileProcessing && results.fileProcessing.score < 70) {
            recommendations.push('优化文件处理并发策略');
        }

        if (results.uiResponse && results.uiResponse.score < 70) {
            recommendations.push('优化UI渲染和事件处理');
        }

        if (recommendations.length === 0) {
            recommendations.push('系统性能表现良好，继续保持');
        }

        return recommendations;
    }

    /**
     * 获取测试历史
     * @SERVICE 测试历史获取方法
     * @returns {Array} 历史测试结果
     */
    getTestHistory() {
        return Array.from(this.testResults.entries()).map(([timestamp, results]) => ({
            timestamp: new Date(timestamp).toISOString(),
            results: results
        }));
    }

    /**
     * 清除测试历史
     * @SERVICE 测试历史清除方法
     */
    clearTestHistory() {
        this.testResults.clear();
        console.log('🧹 测试历史已清除');
    }

    /**
     * 销毁性能测试器
     * @LIFECYCLE 性能测试器销毁方法
     */
    destroy() {
        this.clearTestHistory();
        this.benchmarks.clear();
        this.performanceMonitor = null;
        console.log('🗑️ PerformanceTester 已销毁');
    }
}

// ==================== 全局实例 ====================
let globalPerformanceTester = null;

/**
 * 获取全局性能测试器实例
 * @SERVICE 全局性能测试器获取函数
 * @returns {PerformanceTester} 性能测试器实例
 */
export function getPerformanceTester() {
    if (!globalPerformanceTester) {
        globalPerformanceTester = new PerformanceTester();
    }
    return globalPerformanceTester;
}

// ==================== 便捷函数 ====================

/**
 * 运行性能测试
 * @SERVICE 性能测试运行函数
 * @returns {Promise<Object>} 测试结果
 */
export async function runPerformanceTest() {
    return await getPerformanceTester().runFullPerformanceTest();
}

/**
 * 快速性能检查
 * @SERVICE 快速性能检查函数
 * @returns {Object} 检查结果
 */
export function quickPerformanceCheck() {
    const tester = getPerformanceTester();
    
    return {
        memoryUsage: tester.getMemoryUsage(),
        timestamp: Date.now(),
        loadedModules: window.ModuleExports ? Object.keys(window.ModuleExports).length : 0,
        userAgent: navigator.userAgent
    };
}
