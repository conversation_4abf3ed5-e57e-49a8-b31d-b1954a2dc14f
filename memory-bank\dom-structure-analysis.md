# 🏗️ DOM结构分析 - standalone.html关键元素提取

## 📋 分析概述 [RS:5]

**分析目标**: 提取standalone.html中的关键DOM元素和ID，确保新的index.html保持UI契约不变
**分析范围**: 31,242行代码中的DOM结构、CSS类和JavaScript事件绑定
**发现总数**: 795个带有id或class属性的元素

## 🎯 关键DOM容器 [RS:5]

### 核心应用结构
```html
<!-- 页面头部 -->
<header class="header" role="banner">
  <div class="container">
    <div class="header-content">
      <h1 class="app-title">GoMyHire 对话分析平台</h1>
    </div>
  </div>
</header>

<!-- 主内容区域 -->
<main class="main" role="main">
  <div class="container">
    <!-- 功能导航标签页 -->
    <nav class="tabs-container" role="navigation">
      <div class="tabs-nav" role="tablist">
        <button class="tab-btn active" data-tab="analysis">数据分析</button>
        <button class="tab-btn" data-tab="reports">详细报告</button>
        <button class="tab-btn" data-tab="qa-dataset">问答题集</button>
      </div>
    </nav>
  </div>
</main>
```

## 📁 文件上传相关元素 [RS:5]

### 关键文件输入控件
```html
<!-- 司机客服对话分析 -->
<input type="file" id="driver-customer-file-input" multiple accept=".txt,.json,.csv" class="hidden">
<input type="file" id="driver-customer-folder-input" webkitdirectory multiple class="hidden">

<!-- 客人客服对话分析 -->
<input type="file" id="guest-customer-file-input" multiple accept=".txt,.json,.csv" class="hidden">
<input type="file" id="guest-customer-folder-input" webkitdirectory multiple class="hidden">

<!-- 内部运营对话分析 -->
<input type="file" id="internal-ops-file-input" multiple accept=".txt,.json,.csv" class="hidden">
<input type="file" id="internal-ops-folder-input" webkitdirectory multiple class="hidden">
```

### 文件列表容器
```html
<div class="file-list-container" id="driver-customer-files">
  <div class="file-list" role="list"></div>
</div>

<div class="file-list-container" id="guest-customer-files">
  <div class="file-list" id="file-list-main">
    <div id="file-list-loading" class="loading-state hidden"></div>
    <div id="file-list-empty" class="empty-state hidden"></div>
    <div id="file-list-content" class="file-list-content" role="list"></div>
  </div>
</div>
```

## 🎮 控制按钮元素 [RS:5]

### 主要操作按钮
```html
<!-- 开始分析按钮 -->
<button type="button" id="start-btn" class="btn-primary" disabled>
  <i class="fas fa-play"></i> 开始分析
</button>

<!-- 测试显示按钮 -->
<button type="button" id="test-display-btn" class="btn-secondary">
  <i class="fas fa-vial"></i> 测试图表表格显示
</button>

<!-- 导出相关按钮 -->
<button type="button" id="clear-results-btn" class="btn-sm btn-outline-danger hidden">
  <i class="fas fa-trash"></i> 清空
</button>
```

### 文件管理按钮
```html
<button type="button" id="refresh-file-list-btn" class="btn-sm btn-outline-primary">
  <i class="fas fa-sync-alt"></i> 刷新
</button>

<button type="button" id="clear-all-files-btn" class="btn-sm btn-outline-danger">
  <i class="fas fa-trash-alt"></i> 清空列表
</button>
```

## 📊 数据统计显示元素 [RS:4]

### 主要统计指标
```html
<!-- 司机数量 -->
<div class="stat-value" id="main-drivers-count">0</div>

<!-- 客服数量 -->
<div class="stat-value" id="main-agents-count">0</div>

<!-- 知识库条目 -->
<div class="stat-value" id="main-knowledge-count">0</div>

<!-- 问答题集 -->
<div class="stat-value" id="main-qa-count">0</div>

<!-- 质量指标 -->
<div class="stat-value" id="main-avg-satisfaction">0</div>
<div class="stat-value" id="main-avg-effectiveness">0</div>
<div class="stat-value" id="main-avg-response-time">0</div>
<div class="stat-value" id="main-common-qa-count">0</div>
```

### 文件统计信息
```html
<span id="total-files-count" class="stat-value">0</span>
<span id="completed-files-count" class="stat-value">0</span>
<span id="processing-files-count" class="stat-value">0</span>
<span id="failed-files-count" class="stat-value">0</span>
```

## 📈 图表容器元素 [RS:5]

### 核心图表容器
```html
<!-- 问题分布图表 -->
<div id="chart-questions" class="chart" role="img"></div>

<!-- 有效性分析图表 -->
<div id="chart-effectiveness" class="chart" role="img"></div>

<!-- 满意度统计图表 -->
<div id="chart-satisfaction" class="chart" role="img"></div>

<!-- 知识库图表 -->
<div id="chart-knowledge" class="chart"></div>

<!-- 问答难度分布图表 -->
<div id="chart-qa-difficulty" class="chart"></div>

<!-- 问答标签分布图表 -->
<div id="chart-qa-tags" class="chart"></div>
```

### 图表预览表格
```html
<tbody id="questions-preview-tbody"></tbody>
<tbody id="effectiveness-preview-tbody"></tbody>
<tbody id="satisfaction-preview-tbody"></tbody>
<tbody id="knowledge-preview-tbody"></tbody>
<tbody id="qa-difficulty-preview-tbody"></tbody>
<tbody id="qa-tags-preview-tbody"></tbody>
```

## 📋 数据表格元素 [RS:4]

### 实时结果表格
```html
<table class="results-table" id="results-table" role="table">
  <tbody id="results-tbody">
    <!-- 动态生成的结果行 -->
  </tbody>
</table>
```

### 结果计数显示
```html
<span class="text-muted" id="results-count">0 条记录</span>
```

## 🔄 状态和进度元素 [RS:3]

### 上传进度显示
```html
<div class="upload-progress-bar" role="progressbar">
  <div class="upload-progress-fill" id="upload-progress-fill"></div>
</div>
<div class="upload-progress-text" id="upload-progress-text">准备中...</div>
```

### 日志系统
```html
<span id="log-count" class="text-muted">(0)</span>
<div class="log-content" id="log-content">
  <div class="log-box" id="system-log-box"></div>
</div>
<i class="fas fa-chevron-down log-toggle-icon" id="log-toggle-icon"></i>
```

## 🎛️ 筛选和搜索元素 [RS:3]

### 文件筛选控件
```html
<input type="text" id="file-search-input" placeholder="搜索文件名...">
<select id="file-status-filter" class="filter-select">
  <option value="">全部状态</option>
  <option value="pending">等待中</option>
  <option value="processing">处理中</option>
  <option value="completed">已完成</option>
  <option value="failed">失败</option>
</select>
<select id="file-sort-select" class="filter-select">
  <option value="uploadTime-desc">最新上传</option>
  <option value="uploadTime-asc">最早上传</option>
</select>
```

### 问答数据筛选
```html
<input type="text" id="qa-search" placeholder="搜索问题或答案...">
<select id="qa-difficulty-filter" class="form-control">
  <option value="">所有难度</option>
  <option value="1">简单</option>
  <option value="5">复杂</option>
</select>
<select id="qa-common-filter" class="form-control">
  <option value="">所有类型</option>
  <option value="true">通用问题</option>
  <option value="false">特殊问题</option>
</select>
```

## 📄 标签页内容元素 [RS:3]

### 主要标签页容器
```html
<!-- 数据分析标签页 -->
<section class="tab-content active" id="analysis-panel" role="tabpanel"></section>

<!-- 详细报告标签页 -->
<div id="tab-reports" class="tab-content"></div>

<!-- 问答题集标签页 -->
<div id="tab-qa-dataset" class="tab-content"></div>
```

## 🎨 CSS类命名模式 [RS:2]

### 主要CSS类模式
- **容器类**: `.container`, `.card`, `.card-header`, `.card-body`
- **按钮类**: `.btn-primary`, `.btn-secondary`, `.btn-sm`, `.btn-outline-*`
- **状态类**: `.active`, `.hidden`, `.disabled`, `.loading-state`, `.empty-state`
- **图标类**: `.fas fa-*` (Font Awesome图标)
- **布局类**: `.d-flex-gap-sm`, `.align-items-center`, `.text-muted`

### 功能特定类
- **文件相关**: `.file-list`, `.file-item`, `.file-info`, `.file-status`
- **图表相关**: `.chart`, `.chart-container`, `.chart-preview`
- **统计相关**: `.stat-item`, `.stat-value`, `.stat-label`
- **标签页相关**: `.tab-btn`, `.tab-content`, `.tabs-container`

## ⚠️ 关键约束和注意事项 [RS:4]

### 必须保持的ID
1. **文件输入**: `*-file-input`, `*-folder-input`
2. **图表容器**: `chart-questions`, `chart-effectiveness`, `chart-satisfaction`, `chart-knowledge`
3. **统计显示**: `main-*-count`, `main-avg-*`
4. **控制按钮**: `start-btn`, `clear-results-btn`
5. **数据表格**: `results-table`, `results-tbody`

### 必须保持的CSS类
1. **响应式布局**: `.container`, `.card`, `.card-header`
2. **按钮样式**: `.btn-primary`, `.btn-secondary`, `.btn-sm`
3. **状态指示**: `.active`, `.hidden`, `.loading-state`
4. **图表样式**: `.chart`, `.chart-container`

### JavaScript事件绑定依赖
- 大量onclick事件绑定到特定ID
- data-*属性用于功能识别
- role和aria-*属性用于无障碍访问

## 📝 重构建议 [RS:3]

### 保持不变的元素
- 所有带有JavaScript事件绑定的ID
- 图表容器的ID和基本结构
- 主要导航和标签页结构
- 文件上传相关的input元素

### 可以优化的部分
- 简化嵌套的div结构
- 合并重复的CSS类
- 优化无障碍访问属性
- 减少内联样式使用
