# GoMyHire 对话分析系统 - 技术文档

## 📋 目录

1. [架构概述](#架构概述)
2. [模块设计](#模块设计)
3. [性能优化](#性能优化)
4. [API接口](#api接口)
5. [开发指南](#开发指南)

## 架构概述

### 系统架构图

```
┌─────────────────────────────────────────────────────────┐
│                    Browser Environment                   │
├─────────────────────────────────────────────────────────┤
│  index.html (Entry Point)                              │
│  ├── CDN Libraries (ECharts, Papa Parse)               │
│  └── loader.js (Module Loader)                         │
├─────────────────────────────────────────────────────────┤
│                    ES6 Modules                          │
│  ├── constants.js    (Global Constants)                │
│  ├── storage.js      (Data Storage & Cache)            │
│  ├── parser.js       (Text Parsing & AI)               │
│  ├── charts.js       (Data Visualization)              │
│  ├── drag-upload.js  (File Upload)                     │
│  └── main.js         (Application Core)                │
├─────────────────────────────────────────────────────────┤
│                External Services                        │
│  ├── Kimi API        (AI Analysis)                     │
│  ├── localStorage    (Data Persistence)                │
│  └── File System     (File I/O)                        │
└─────────────────────────────────────────────────────────┘
```

### 技术栈

| 层级 | 技术 | 版本 | 用途 |
|------|------|------|------|
| 前端框架 | 原生JavaScript | ES6+ | 核心逻辑 |
| 模块系统 | ES6 Modules | - | 代码组织 |
| 图表库 | ECharts | 5.x | 数据可视化 |
| 数据处理 | Papa Parse | 5.x | CSV处理 |
| AI服务 | Kimi API | v1 | 智能分析 |
| 存储 | localStorage | - | 数据持久化 |

## 模块设计

### 1. constants.js - 全局常量

```javascript
// 核心配置
const MAX_CONCURRENCY = 50;        // 最大并发数
const REQUEST_TIMEOUT = 90000;      // 请求超时
const RETRY_ATTEMPTS = 3;           // 重试次数

// API端点
const API_ENDPOINTS = {
    KIMI: 'https://api.moonshot.cn/v1/chat/completions'
};

// 存储键名
const STORAGE_KEYS = {
    ANALYSIS_RESULTS: 'analysisResults',
    FILE_HISTORY: 'fileHistory',
    USER_SETTINGS: 'userSettings'
};
```

**设计原则**:
- 集中管理所有常量
- 避免魔法数字
- 便于配置调整

### 2. storage.js - 数据存储

```javascript
class AppDataManager {
    constructor() {
        this.memoryCache = new Map();           // 内存缓存
        this.maxMemoryCacheSize = 50;           // 缓存大小限制
        this.compressionThreshold = 10000;      // 压缩阈值
    }
    
    // 核心方法
    save(key, data)                 // 保存数据
    load(key, defaultValue)         // 加载数据
    remove(key)                     // 删除数据
    clearAll()                      // 清空所有数据
    
    // 性能优化
    compressString(str)             // 字符串压缩
    updateMemoryCache(key, data)    // 内存缓存管理
    getMemoryStats()                // 内存使用统计
}
```

**特性**:
- LRU内存缓存
- 大数据自动压缩
- 存储使用统计
- 数据迁移支持

### 3. parser.js - 文本解析

```javascript
class TextParser {
    constructor() {
        this.parseCache = new Map();        // 解析结果缓存
        this.analysisCache = new Map();     // AI分析缓存
        this.maxCacheSize = 100;            // 缓存大小
    }
    
    // 核心解析
    parseTxtContent(content, fileName)      // 解析文本内容
    parseTimestamp(dateStr, timeStr)        // 时间戳解析
    validateParseResult(result)             // 结果验证
    
    // AI分析
    evaluateConversationWithKimi(data, key) // Kimi API调用
    buildAnalysisPrompt(data)               // 构建分析提示
    parseAIAnalysis(text)                   // 解析AI结果
    
    // 批量处理
    parseBatchFiles(files, callback)        // 批量文件处理
}
```

**算法优化**:
- 正则表达式预编译
- 结果缓存避免重复计算
- 流式处理大文件
- 错误恢复机制

### 4. charts.js - 图表管理

```javascript
class ChartManager {
    constructor() {
        this.charts = new Map();            // 图表实例缓存
        this.chartConfigs = {};             // 图表配置模板
    }
    
    // 图表生命周期
    initChart(containerId, type)            // 初始化图表
    updateChart(containerId, data)          // 更新图表数据
    destroyChart(containerId)               // 销毁图表
    
    // 批量操作
    updateAllCharts(data)                   // 批量更新
    resetAllCharts()                        // 批量重置
    
    // 导出功能
    exportChart(containerId, filename)      // 导出图片
}
```

**图表类型**:
- 饼图：问题分类分布
- 柱状图：评分分布
- 环形图：满意度统计
- 组合图：综合分析

### 5. main.js - 主应用

```javascript
class TaskPool {
    constructor(maxConcurrency = 50) {
        this.running = new Set();           // 运行中任务
        this.queue = [];                    // 任务队列
        this.completed = [];                // 完成任务
        this.failed = [];                   // 失败任务
    }
    
    addTask(taskFn, taskData)               // 添加任务
    processQueue()                          // 处理队列
    waitAll()                               // 等待所有任务
}

class MainApp {
    constructor() {
        this.taskPool = null;               // 任务池
        this.modules = {};                  // 模块引用
        this.appState = {};                 // 应用状态
    }
    
    init()                                  // 应用初始化
    processBatchFiles(files)                // 批量处理
    processFileWithAI(file)                 // AI处理单文件
}
```

## 性能优化

### 1. 模块懒加载

```javascript
// 懒加载配置
const LAZY_MODULES = {
    'charts.js': () => document.getElementById('chart-questions') !== null,
    'drag-upload.js': () => document.getElementById('drop-zone') !== null
};

// 按需加载
async function loadLazyModule(moduleName) {
    if (LAZY_MODULES[moduleName]) {
        return await loadModuleWithDependencies(moduleName);
    }
}
```

**优势**:
- 减少初始加载时间
- 降低内存占用
- 提升用户体验

### 2. 结果缓存

```javascript
// 解析结果缓存
parseTxtContent(content, fileName) {
    const cacheKey = this.generateCacheKey(content, fileName);
    if (this.parseCache.has(cacheKey)) {
        return this.parseCache.get(cacheKey);
    }
    // ... 解析逻辑
    this.setCacheItem(this.parseCache, cacheKey, result);
    return result;
}
```

**缓存策略**:
- LRU淘汰算法
- 内存使用监控
- 自动清理机制

### 3. 并发控制

```javascript
class TaskPool {
    async addTask(taskFn, taskData) {
        return new Promise((resolve, reject) => {
            const task = { taskFn, taskData, resolve, reject };
            this.queue.push(task);
            this.processQueue();
        });
    }
    
    processQueue() {
        while (this.running.size < this.maxConcurrency && this.queue.length > 0) {
            const task = this.queue.shift();
            this.runTask(task);
        }
    }
}
```

**并发优化**:
- 任务队列管理
- 动态并发调整
- 错误隔离处理

### 4. 内存管理

```javascript
// 内存缓存管理
updateMemoryCache(key, data) {
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
        const firstKey = this.memoryCache.keys().next().value;
        this.memoryCache.delete(firstKey);
    }
    this.memoryCache.set(key, data);
}

// 内存使用统计
getMemoryStats() {
    let totalSize = 0;
    for (const [key, value] of this.memoryCache) {
        totalSize += JSON.stringify(value).length;
    }
    return { totalMemoryBytes: totalSize };
}
```

## API接口

### 1. Kimi API集成

```javascript
async evaluateConversationWithKimi(conversationData, apiKey) {
    const requestBody = {
        model: 'kimi-k2-turbo-preview',
        messages: [
            {
                role: 'system',
                content: '你是一个专业的客服对话分析师...'
            },
            {
                role: 'user',
                content: this.buildAnalysisPrompt(conversationData)
            }
        ],
        temperature: 0.3,
        max_tokens: 1000
    };
    
    const response = await fetch('https://api.moonshot.cn/v1/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(90000)
    });
    
    return this.parseAIAnalysis(response);
}
```

### 2. 错误处理

```javascript
// 统一错误处理
try {
    const result = await apiCall();
    return result;
} catch (error) {
    if (error.name === 'AbortError') {
        throw new Error('请求超时');
    } else if (error.status === 401) {
        throw new Error('API密钥无效');
    } else if (error.status === 429) {
        throw new Error('请求频率过高');
    } else {
        throw new Error(`API调用失败: ${error.message}`);
    }
}
```

## 开发指南

### 1. 添加新模块

```javascript
// 1. 在src/目录创建新模块文件
// 2. 更新loader.js中的MODULE_GRAPH
const MODULE_GRAPH = {
    'new-module.js': ['constants.js'],  // 添加依赖关系
    // ... 其他模块
};

// 3. 实现模块导出
class NewModule {
    constructor() {
        // 初始化逻辑
    }
}

export default new NewModule();
```

### 2. 性能监控

```javascript
// 添加性能监控
const startTime = performance.now();
// ... 执行代码
const endTime = performance.now();
console.log(`执行时间: ${endTime - startTime}ms`);

// 内存使用监控
const memoryInfo = performance.memory;
console.log(`内存使用: ${memoryInfo.usedJSHeapSize / 1024 / 1024}MB`);
```

### 3. 调试技巧

```javascript
// 开启调试模式
window.LOCAL_CONFIG = {
    debug: true,
    logLevel: 'debug'
};

// 使用调试工具
console.group('模块加载');
console.log('加载模块:', moduleName);
console.groupEnd();

// 性能分析
console.time('解析文件');
// ... 解析逻辑
console.timeEnd('解析文件');
```

### 4. 测试策略

```javascript
// 单元测试示例
function testParseTimestamp() {
    const parser = new TextParser();
    const result = parser.parseTimestamp('02/01/2025', '13:51:11');
    
    assert(result > 0, '时间戳应该大于0');
    assert(typeof result === 'number', '时间戳应该是数字');
    
    console.log('✅ parseTimestamp测试通过');
}

// 集成测试
async function testFullWorkflow() {
    const files = [mockFile];
    const results = await processBatchFiles(files);
    
    assert(results.length > 0, '应该有处理结果');
    assert(results[0].success, '处理应该成功');
    
    console.log('✅ 完整流程测试通过');
}
```

## 部署指南

### 1. 文件检查清单

- [ ] 所有模块文件完整
- [ ] CDN链接可访问
- [ ] 配置文件正确
- [ ] 测试功能正常

### 2. 性能优化检查

- [ ] 启用懒加载
- [ ] 配置缓存大小
- [ ] 设置并发限制
- [ ] 清理调试代码

### 3. 兼容性测试

- [ ] Chrome 80+
- [ ] Firefox 75+
- [ ] Safari 13+
- [ ] Edge 80+

---

**版本**: v2.0.0  
**最后更新**: 2025-08-13  
**开发团队**: Augment Agent
