# 📋 配置指南

## 🚀 快速开始

### 1. 创建本地配置文件

```bash
# 复制配置模板
cp local-config.example.js local-config.js
```

### 2. 配置Kimi API密钥

编辑 `local-config.js` 文件，设置您的API密钥：

```javascript
window.LOCAL_CONFIG = {
    apiKey: 'sk-your-actual-kimi-api-key-here',
    // ... 其他配置
};
```

### 3. 获取Kimi API密钥

1. 访问 [Kimi开放平台](https://platform.moonshot.cn/)
2. 注册账号并登录
3. 在控制台中创建API密钥
4. 复制密钥到配置文件中

## ⚙️ 配置选项详解

### API配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `apiKey` | string | - | **必填** Kimi API密钥 |
| `apiBaseUrl` | string | `https://api.moonshot.cn/v1` | API基础URL |
| `model` | string | `kimi-k2-turbo-preview` | 使用的AI模型 |

### 性能配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `maxConcurrency` | number | 50 | 最大并发请求数 (1-50) |
| `requestTimeout` | number | 90000 | 请求超时时间 (毫秒) |
| `retryAttempts` | number | 3 | 失败重试次数 |

### 用户偏好

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `theme` | string | `light` | 界面主题 (`light`/`dark`/`auto`) |
| `language` | string | `zh-CN` | 语言设置 |
| `pageSize` | number | 20 | 分页大小 |
| `autoSaveInterval` | number | 30000 | 自动保存间隔 (毫秒) |

## 🔧 高级配置

### 调试模式

```javascript
{
    debug: true,                    // 启用调试日志
    performanceMonitoring: true    // 启用性能监控
}
```

### 自定义分析提示词

```javascript
{
    customPrompts: {
        effectiveness: '请分析客服回复的专业性和有效性，评分1-5分',
        satisfaction: '请评估客户在对话中的满意度，评分1-5分',
        category: '请为这个问题选择最合适的分类'
    }
}
```

### 导出设置

```javascript
{
    defaultExportFormat: 'csv',     // 默认导出格式
    exportFilePrefix: 'analysis',   // 文件名前缀
    includeRawData: true           // 包含原始数据
}
```

## 🛡️ 安全注意事项

### API密钥安全

- ❌ **不要**将包含真实API密钥的配置文件提交到版本控制系统
- ✅ **务必**将 `local-config.js` 添加到 `.gitignore`
- ✅ **建议**定期轮换API密钥
- ✅ **建议**为不同环境使用不同的API密钥

### 权限控制

```javascript
{
    maxConcurrency: 10,    // 降低并发数以避免API限流
    requestTimeout: 60000  // 适当的超时时间
}
```

## 🔍 故障排除

### 常见问题

#### 1. API密钥无效

**错误信息**: `API调用失败: 401 Unauthorized`

**解决方案**:
- 检查API密钥是否正确
- 确认API密钥是否已激活
- 检查账户余额是否充足

#### 2. 请求超时

**错误信息**: `请求超时`

**解决方案**:
```javascript
{
    requestTimeout: 120000,  // 增加超时时间到2分钟
    retryAttempts: 5        // 增加重试次数
}
```

#### 3. 并发限制

**错误信息**: `Too Many Requests`

**解决方案**:
```javascript
{
    maxConcurrency: 5,      // 降低并发数
    retryDelay: 2000       // 增加重试延迟
}
```

### 配置验证

系统会自动验证配置的有效性：

```javascript
// 在浏览器控制台中检查配置
console.log('当前配置:', window.LOCAL_CONFIG);

// 手动验证配置
validateConfig();
```

## 📝 配置模板

### 基础配置

```javascript
window.LOCAL_CONFIG = {
    apiKey: 'sk-your-api-key',
    maxConcurrency: 20,
    requestTimeout: 90000,
    theme: 'light',
    pageSize: 20
};
```

### 高性能配置

```javascript
window.LOCAL_CONFIG = {
    apiKey: 'sk-your-api-key',
    maxConcurrency: 50,
    requestTimeout: 60000,
    retryAttempts: 2,
    cacheTimeout: 600000,  // 10分钟缓存
    performanceMonitoring: true
};
```

### 开发调试配置

```javascript
window.LOCAL_CONFIG = {
    apiKey: 'sk-your-dev-api-key',
    maxConcurrency: 5,
    requestTimeout: 120000,
    debug: true,
    performanceMonitoring: true,
    enableNotifications: false
};
```

## 📞 技术支持

如果您在配置过程中遇到问题：

1. 检查浏览器控制台的错误信息
2. 参考本文档的故障排除部分
3. 确认API密钥和网络连接正常
4. 尝试使用基础配置模板

---

**注意**: 配置文件中的所有设置都是可选的，系统会为未配置的项目使用合理的默认值。
