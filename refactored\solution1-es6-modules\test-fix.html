<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块加载修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        #console-output { 
            background: #000; 
            color: #0f0; 
            padding: 10px; 
            height: 300px; 
            overflow-y: auto; 
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 模块加载修复测试</h1>
    
    <div id="test-results"></div>
    
    <h2>控制台输出</h2>
    <div id="console-output"></div>
    
    <script>
        // 捕获控制台输出
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            consoleOutput.textContent += '[LOG] ' + args.join(' ') + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            consoleOutput.textContent += '[ERROR] ' + args.join(' ') + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            originalError.apply(console, args);
        };
        
        // 测试结果显示
        function showTestResult(name, success, message) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'success' : 'error'}`;
            resultDiv.innerHTML = `<strong>${name}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        // 测试模块加载
        async function testModuleLoading() {
            console.log('🧪 开始测试模块加载修复效果...');
            
            // 初始化全局导出对象
            window.ModuleExports = {};
            
            const testModules = [
                'utils.js',
                'constants.js', 
                'data-processor.js',
                'tag-center.js',
                'qa-optimization.js',
                'module-loader-optimizer.js'
            ];
            
            for (const moduleName of testModules) {
                try {
                    console.log(`📦 测试加载模块: ${moduleName}`);
                    
                    const script = document.createElement('script');
                    script.src = `./src/${moduleName}`;
                    script.setAttribute('data-module', moduleName);
                    
                    await new Promise((resolve, reject) => {
                        script.onload = () => {
                            console.log(`✅ ${moduleName} 加载成功`);
                            
                            // 检查是否正确导出到全局
                            const moduleExports = window.ModuleExports[moduleName];
                            if (moduleExports) {
                                const exportKeys = Object.keys(moduleExports);
                                console.log(`   导出内容: ${exportKeys.join(', ')}`);
                                showTestResult(moduleName, true, `加载成功，导出 ${exportKeys.length} 个对象`);
                            } else {
                                console.log(`⚠️ ${moduleName} 未找到全局导出`);
                                showTestResult(moduleName, false, '加载成功但未找到全局导出');
                            }
                            resolve();
                        };
                        
                        script.onerror = () => {
                            console.log(`❌ ${moduleName} 加载失败`);
                            showTestResult(moduleName, false, '脚本加载失败');
                            reject(new Error(`脚本加载失败: ${moduleName}`));
                        };
                    });
                    
                    document.head.appendChild(script);
                    
                    // 等待一下再加载下一个模块
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                } catch (error) {
                    console.error(`模块 ${moduleName} 测试失败:`, error);
                    showTestResult(moduleName, false, error.message);
                }
            }
            
            console.log('🎯 模块加载测试完成');
            
            // 显示最终统计
            const totalModules = Object.keys(window.ModuleExports).length;
            showTestResult('总结', totalModules > 0, `成功加载 ${totalModules} 个模块`);
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', testModuleLoading);
    </script>
</body>
</html>
