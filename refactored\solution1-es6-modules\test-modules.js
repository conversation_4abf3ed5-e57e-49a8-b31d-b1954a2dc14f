/**
 * 模块化系统测试脚本
 * 测试修复后的ES6模块转换是否成功
 */

// 模拟浏览器环境
global.window = global;
global.document = {
    createElement: () => ({}),
    head: { appendChild: () => {} },
    addEventListener: () => {},
    dispatchEvent: () => {},
    getElementById: () => null,
    querySelectorAll: () => [],
    querySelector: () => null
};
global.localStorage = {
    setItem: () => {},
    getItem: () => null,
    removeItem: () => {}
};
global.performance = { now: () => Date.now() };

// 初始化全局导出对象
global.window.ModuleExports = {};

console.log('🧪 开始测试模块化系统...\n');

// 测试函数
function testModule(modulePath, moduleName) {
    try {
        console.log(`📦 测试 ${moduleName}...`);
        
        // 动态加载模块
        delete require.cache[require.resolve(modulePath)];
        require(modulePath);
        
        // 检查是否正确导出到全局
        const moduleExports = global.window.ModuleExports[moduleName];
        if (moduleExports) {
            console.log(`✅ ${moduleName} 加载成功`);
            console.log(`   导出对象包含: ${Object.keys(moduleExports).join(', ')}`);
            return true;
        } else {
            console.log(`❌ ${moduleName} 未找到全局导出`);
            return false;
        }
    } catch (error) {
        console.log(`❌ ${moduleName} 加载失败:`, error.message);
        return false;
    }
}

// 测试核心模块
const testResults = {};

console.log('='.repeat(50));
console.log('测试基础模块');
console.log('='.repeat(50));

testResults.constants = testModule('./src/constants.js', 'constants.js');
testResults.utils = testModule('./src/utils.js', 'utils.js');
testResults.storageManager = testModule('./src/storage-manager.js', 'storage-manager.js');

console.log('\n' + '='.repeat(50));
console.log('测试核心功能模块');
console.log('='.repeat(50));

testResults.eventBus = testModule('./src/event-bus.js', 'event-bus.js');
testResults.performanceMonitor = testModule('./src/performance-monitor.js', 'performance-monitor.js');
testResults.dependencyManager = testModule('./src/dependency-manager.js', 'dependency-manager.js');
testResults.serviceContainer = testModule('./src/service-container.js', 'service-container.js');

console.log('\n' + '='.repeat(50));
console.log('测试数据处理模块');
console.log('='.repeat(50));

testResults.dataProcessor = testModule('./src/data-processor.js', 'data-processor.js');
testResults.parser = testModule('./src/parser.js', 'parser.js');
testResults.storage = testModule('./src/storage.js', 'storage.js');

console.log('\n' + '='.repeat(50));
console.log('测试UI组件模块');
console.log('='.repeat(50));

testResults.notification = testModule('./src/notification.js', 'notification.js');
testResults.tabs = testModule('./src/tabs.js', 'tabs.js');
testResults.modal = testModule('./src/modal.js', 'modal.js');
testResults.progress = testModule('./src/progress.js', 'progress.js');
testResults.ui = testModule('./src/ui.js', 'ui.js');

console.log('\n' + '='.repeat(50));
console.log('测试高级功能模块');
console.log('='.repeat(50));

testResults.charts = testModule('./src/charts.js', 'charts.js');

console.log('\n' + '='.repeat(50));
console.log('测试主应用模块');
console.log('='.repeat(50));

testResults.main = testModule('./src/main.js', 'main.js');

// 统计结果
const totalModules = Object.keys(testResults).length;
const successCount = Object.values(testResults).filter(result => result).length;
const failureCount = totalModules - successCount;

console.log('\n' + '='.repeat(50));
console.log('📊 测试结果统计');
console.log('='.repeat(50));

console.log(`总模块数: ${totalModules}`);
console.log(`✅ 成功: ${successCount}`);
console.log(`❌ 失败: ${failureCount}`);
console.log(`成功率: ${((successCount / totalModules) * 100).toFixed(1)}%`);

if (failureCount > 0) {
    console.log('\n🔍 失败的模块:');
    Object.entries(testResults)
        .filter(([, success]) => !success)
        .forEach(([module]) => console.log(`  - ${module}`));
}

console.log('\n' + '='.repeat(50));
console.log(successCount === totalModules ? '🎉 所有模块测试通过！' : '⚠️  部分模块需要进一步修复');
console.log('='.repeat(50));