/**
 * 简单的模块加载测试脚本
 * 用于验证修复后的模块是否能正确加载
 */

// 模拟浏览器环境
global.window = global;
global.document = { 
    createElement: () => ({ 
        setAttribute: () => {},
        style: {}
    }),
    head: { appendChild: () => {} },
    getElementById: () => null,
    querySelector: () => null,
    addEventListener: () => {}
};
global.console = console;

// 初始化全局导出对象
global.ModuleExports = {};

console.log('🧪 开始测试修复后的模块加载...\n');

// 测试模块列表
const testModules = [
    { name: 'utils.js', path: './src/utils.js' },
    { name: 'constants.js', path: './src/constants.js' },
    { name: 'tag-center.js', path: './src/tag-center.js' },
    { name: 'qa-optimization.js', path: './src/qa-optimization.js' },
    { name: 'module-loader-optimizer.js', path: './src/module-loader-optimizer.js' }
];

let successCount = 0;
let failCount = 0;

// 测试每个模块
for (const module of testModules) {
    try {
        console.log(`📦 测试模块: ${module.name}`);
        
        // 清除require缓存
        delete require.cache[require.resolve(module.path)];
        
        // 加载模块
        require(module.path);
        
        // 检查是否正确导出到全局
        const moduleExports = global.ModuleExports[module.name];
        if (moduleExports) {
            const exportKeys = Object.keys(moduleExports);
            console.log(`✅ ${module.name} 加载成功`);
            console.log(`   导出内容: ${exportKeys.join(', ')}`);
            successCount++;
        } else {
            console.log(`⚠️ ${module.name} 加载成功但未找到全局导出`);
            failCount++;
        }
        
    } catch (error) {
        console.log(`❌ ${module.name} 加载失败: ${error.message}`);
        failCount++;
    }
    
    console.log(''); // 空行分隔
}

// 显示测试结果
console.log('🎯 测试完成');
console.log(`✅ 成功: ${successCount} 个模块`);
console.log(`❌ 失败: ${failCount} 个模块`);
console.log(`📊 成功率: ${Math.round((successCount / (successCount + failCount)) * 100)}%`);

// 显示全局导出状态
const totalExports = Object.keys(global.ModuleExports).length;
console.log(`\n📋 全局导出状态:`);
console.log(`   已注册模块: ${totalExports} 个`);
if (totalExports > 0) {
    console.log(`   模块列表: ${Object.keys(global.ModuleExports).join(', ')}`);
}
