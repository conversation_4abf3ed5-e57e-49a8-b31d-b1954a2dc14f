<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据处理系统集成测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-section h2 {
            color: #2c3e50;
            margin-top: 0;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-button.success {
            background: #27ae60;
        }
        .test-button.error {
            background: #e74c3c;
        }
        .test-results {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #3498db;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #27ae60;
        }
        .error {
            color: #e74c3c;
        }
        .info {
            color: #3498db;
        }
        .warning {
            color: #f39c12;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 数据处理系统集成测试</h1>
        
        <!-- 存储管理器测试 -->
        <div class="test-section">
            <h2>📦 存储管理器测试</h2>
            <button class="test-button" onclick="testStorageManager()">测试基础存储功能</button>
            <button class="test-button" onclick="testEnhancedStorage()">测试增强存储功能</button>
            <button class="test-button" onclick="testStorageStats()">获取存储统计</button>
            <div id="storage-results" class="test-results"></div>
        </div>

        <!-- 数据处理器测试 -->
        <div class="test-section">
            <h2>⚙️ 数据处理器测试</h2>
            <button class="test-button" onclick="testDataProcessor()">测试数据处理功能</button>
            <button class="test-button" onclick="testQADataset()">测试问答数据集</button>
            <button class="test-button" onclick="testDataExport()">测试数据导出</button>
            <div id="processor-results" class="test-results"></div>
        </div>

        <!-- 应用数据管理器测试 -->
        <div class="test-section">
            <h2>🗂️ 应用数据管理器测试</h2>
            <button class="test-button" onclick="testAppDataManager()">测试应用数据管理</button>
            <button class="test-button" onclick="testDataPersistence()">测试数据持久化</button>
            <button class="test-button" onclick="testBatchOperations()">测试批量操作</button>
            <div id="appdata-results" class="test-results"></div>
        </div>

        <!-- 性能监控器测试 -->
        <div class="test-section">
            <h2>📊 性能监控器测试</h2>
            <button class="test-button" onclick="testPerformanceMonitor()">测试性能监控</button>
            <button class="test-button" onclick="testUIPerformance()">测试UI性能监控</button>
            <button class="test-button" onclick="testChartPerformance()">测试图表性能监控</button>
            <div id="performance-results" class="test-results"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h2>🔄 综合集成测试</h2>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="testModuleIntegration()">测试模块集成</button>
            <button class="test-button" onclick="clearAllData()">清空测试数据</button>
            <div id="integration-results" class="test-results"></div>
        </div>

        <!-- 测试统计 -->
        <div class="test-section">
            <h2>📈 测试统计</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="total-tests">0</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="passed-tests">0</div>
                    <div class="stat-label">通过测试</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="failed-tests">0</div>
                    <div class="stat-label">失败测试</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="success-rate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载模块 -->
    <script type="module">
        // 导入所有需要测试的模块
    import { StorageManager, EnhancedStorageManager } from '../src/storage-manager.js';
    import { DataProcessor } from '../src/data-processor.js';
        import { PerformanceTimer, PerformanceMonitor, UIPerformanceMonitor, ChartPerformanceMonitor } from '../src/performance-monitor.js';

        // 全局变量
        window.testModules = {
            StorageManager,
            EnhancedStorageManager,
            DataProcessor,
            AppDataManager: window.ModuleExports?.['storage.js']?.AppDataManager,
            PerformanceTimer,
            PerformanceMonitor,
            UIPerformanceMonitor,
            ChartPerformanceMonitor
        };

        // 测试统计
        window.testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // 日志函数
        window.log = function(message, type = 'info', containerId = 'integration-results') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        };

        // 测试断言函数
        window.assert = function(condition, message, containerId = 'integration-results') {
            window.testStats.total++;
            if (condition) {
                window.testStats.passed++;
                window.log(`✅ ${message}`, 'success', containerId);
                return true;
            } else {
                window.testStats.failed++;
                window.log(`❌ ${message}`, 'error', containerId);
                return false;
            }
        };

        // 更新测试统计
        window.updateTestStats = function() {
            document.getElementById('total-tests').textContent = window.testStats.total;
            document.getElementById('passed-tests').textContent = window.testStats.passed;
            document.getElementById('failed-tests').textContent = window.testStats.failed;
            const successRate = window.testStats.total > 0 
                ? Math.round((window.testStats.passed / window.testStats.total) * 100)
                : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
        };

        // 清空结果容器
        window.clearResults = function(containerId) {
            document.getElementById(containerId).innerHTML = '';
        };

        console.log('✅ 测试环境初始化完成');
        window.log('🚀 测试环境初始化完成，可以开始测试');
    </script>

    <!-- 测试函数 -->
    <script>
        // 存储管理器测试
        async function testStorageManager() {
            clearResults('storage-results');
            log('开始测试存储管理器...', 'info', 'storage-results');
            
            try {
                const storage = new testModules.StorageManager();
                
                // 测试基础保存和加载
                const testData = { name: '测试数据', value: 123, timestamp: Date.now() };
                const saveResult = storage.save('test-key', testData);
                assert(saveResult, '数据保存成功', 'storage-results');
                
                const loadedData = storage.load('test-key');
                assert(JSON.stringify(loadedData) === JSON.stringify(testData), '数据加载正确', 'storage-results');
                
                // 测试存在性检查
                assert(storage.exists('test-key'), '键存在性检查正确', 'storage-results');
                assert(!storage.exists('non-existent-key'), '不存在键检查正确', 'storage-results');
                
                // 测试批量操作
                const batchData = {
                    'batch-1': { value: 1 },
                    'batch-2': { value: 2 },
                    'batch-3': { value: 3 }
                };
                const batchResult = storage.saveBatch(batchData);
                assert(batchResult.success === 3 && batchResult.failed === 0, '批量保存成功', 'storage-results');
                
                // 测试存储信息
                const storageInfo = storage.getStorageInfo();
                assert(storageInfo.totalKeys >= 4, '存储信息获取正确', 'storage-results');
                
                log('存储管理器测试完成', 'success', 'storage-results');
            } catch (error) {
                log(`存储管理器测试失败: ${error.message}`, 'error', 'storage-results');
            }
            
            updateTestStats();
        }

        // 增强存储管理器测试
        async function testEnhancedStorage() {
            clearResults('storage-results');
            log('开始测试增强存储管理器...', 'info', 'storage-results');
            
            try {
                const enhancedStorage = new testModules.EnhancedStorageManager();
                
                // 测试智能存储
                const largeData = { 
                    content: 'x'.repeat(2000), // 大于压缩阈值
                    metadata: { type: 'test', priority: 'high' }
                };
                
                const storeResult = await enhancedStorage.store('large-test', largeData, {
                    type: 'test-data',
                    priority: 'high'
                });
                assert(storeResult, '智能存储成功', 'storage-results');
                
                // 测试智能检索
                const retrievedData = await enhancedStorage.retrieve('large-test');
                assert(retrievedData && retrievedData.content === largeData.content, '智能检索成功', 'storage-results');
                
                // 测试性能指标
                const metrics = enhancedStorage.getPerformanceMetrics();
                assert(metrics.writes >= 1, '性能指标记录正确', 'storage-results');
                
                log('增强存储管理器测试完成', 'success', 'storage-results');
            } catch (error) {
                log(`增强存储管理器测试失败: ${error.message}`, 'error', 'storage-results');
            }
            
            updateTestStats();
        }

        // 存储统计测试
        function testStorageStats() {
            clearResults('storage-results');
            log('开始测试存储统计...', 'info', 'storage-results');
            
            try {
                const storage = new testModules.StorageManager();
                const stats = storage.getStats();
                
                log(`存储统计信息: ${JSON.stringify(stats, null, 2)}`, 'info', 'storage-results');
                assert(typeof stats.totalKeys === 'number', '统计信息格式正确', 'storage-results');
                
                log('存储统计测试完成', 'success', 'storage-results');
            } catch (error) {
                log(`存储统计测试失败: ${error.message}`, 'error', 'storage-results');
            }
            
            updateTestStats();
        }

        // 数据处理器测试
        function testDataProcessor() {
            clearResults('processor-results');
            log('开始测试数据处理器...', 'info', 'processor-results');
            
            try {
                const processor = new testModules.DataProcessor();
                
                // 测试统计计算
                const stats = processor.calculateStatistics();
                assert(typeof stats === 'object', '统计计算功能正常', 'processor-results');
                
                // 测试问答项目添加
                const qaData = {
                    question: '测试问题',
                    answer: '测试答案',
                    difficulty: 'easy',
                    tags: ['测试', '示例']
                };
                
                const addResult = processor.addQAItem(qaData);
                assert(addResult, '问答项目添加成功', 'processor-results');
                
                // 测试搜索功能
                const searchResults = processor.searchQAItems('测试');
                assert(searchResults.length > 0, '搜索功能正常', 'processor-results');
                
                log('数据处理器测试完成', 'success', 'processor-results');
            } catch (error) {
                log(`数据处理器测试失败: ${error.message}`, 'error', 'processor-results');
            }
            
            updateTestStats();
        }

        // 运行所有测试
        async function runAllTests() {
            clearResults('integration-results');
            log('🚀 开始运行所有测试...', 'info', 'integration-results');
            
            // 重置统计
            window.testStats = { total: 0, passed: 0, failed: 0 };
            
            await testStorageManager();
            await testEnhancedStorage();
            testStorageStats();
            testDataProcessor();
            
            log(`📊 所有测试完成! 总计: ${window.testStats.total}, 通过: ${window.testStats.passed}, 失败: ${window.testStats.failed}`, 'info', 'integration-results');
            updateTestStats();
        }

        // 清空测试数据
        function clearAllData() {
            try {
                const storage = new testModules.StorageManager();
                const cleared = storage.clear();
                log(`🧹 已清空 ${cleared} 个测试数据项`, 'info', 'integration-results');
            } catch (error) {
                log(`清空数据失败: ${error.message}`, 'error', 'integration-results');
            }
        }

        // 其他测试函数的占位符
        function testQADataset() { log('QA数据集测试 - 待实现', 'warning', 'processor-results'); }
        function testDataExport() { log('数据导出测试 - 待实现', 'warning', 'processor-results'); }
        function testAppDataManager() { log('应用数据管理器测试 - 待实现', 'warning', 'appdata-results'); }
        function testDataPersistence() { log('数据持久化测试 - 待实现', 'warning', 'appdata-results'); }
        function testBatchOperations() { log('批量操作测试 - 待实现', 'warning', 'appdata-results'); }
        function testPerformanceMonitor() { log('性能监控测试 - 待实现', 'warning', 'performance-results'); }
        function testUIPerformance() { log('UI性能监控测试 - 待实现', 'warning', 'performance-results'); }
        function testChartPerformance() { log('图表性能监控测试 - 待实现', 'warning', 'performance-results'); }
        function testModuleIntegration() { log('模块集成测试 - 待实现', 'warning', 'integration-results'); }
    </script>
</body>
</html>
