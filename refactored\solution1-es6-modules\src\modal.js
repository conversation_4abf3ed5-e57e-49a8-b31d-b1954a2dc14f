/* === Naming Conventions Header (auto-injected 2025-08-14) ===
    Modal-related constants should use MODAL_* prefix.
*/
/**
 * ==================== GoMyHire 对话分析系统 - 模态框系统 ====================
 * @SERVICE 从standalone.html完整迁移的模态框系统
 * 实现模态对话框管理，支持动态创建、事件处理、层级管理等功能
 */

// 获取工具函数
function getUtils() {
    return window.ModuleExports && window.ModuleExports['utils.js'];
}

function generateUniqueId(prefix) {
    const utils = getUtils();
    return utils?.generateUniqueId(prefix) || `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function safeExecute(fn) {
    const utils = getUtils();
    return utils?.safeExecute(fn) || (() => {
        try {
            return fn();
        } catch (error) {
            console.error('Safe execute error:', error);
            return null;
        }
    })();
}

// 模态框类型常量
const MODAL_TYPES = {
    DIALOG: 'dialog',
    ALERT: 'alert',
    CONFIRM: 'confirm',
    PROMPT: 'prompt',
    CUSTOM: 'custom'
};

// 模态框大小常量
const MODAL_SIZES = {
    SMALL: 'small',
    MEDIUM: 'medium',
    LARGE: 'large',
    EXTRA_LARGE: 'extra-large',
    FULL: 'full'
};

// ==================== 模态框管理器类 ====================
/**
 * 模态框管理器类 - 负责管理所有模态框的显示、隐藏和生命周期
 * @COMPONENT 模态框管理器
 */
class ModalManager {
    constructor() {
        this.modals = new Map(); // 存储所有活跃的模态框
        this.zIndexBase = 9999; // 基础z-index值
        this.currentZIndex = this.zIndexBase; // 当前z-index值
        this.backdropClickClose = true; // 点击背景是否关闭模态框
        this.escapeKeyClose = true; // 按ESC键是否关闭模态框
        
        this.initializeEventListeners();
        console.log('🪟 ModalManager 初始化完成');
    }

    /**
     * 初始化事件监听器
     * @INIT 事件监听器初始化方法
     */
    initializeEventListeners() {
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.escapeKeyClose) {
                this.closeTopModal();
            }
        });
    }

    /**
     * 显示模态框
     * @SERVICE 模态框显示方法
     * @param {Object} config - 模态框配置
     * @returns {string} 模态框ID
     */
    show(config) {
        const modalConfig = {
            type: config.type || MODAL_TYPES.DIALOG,
            size: config.size || MODAL_SIZES.MEDIUM,
            title: config.title || '',
            content: config.content || '',
            closable: config.closable !== false,
            backdrop: config.backdrop !== false,
            backdropClose: config.backdropClose !== false,
            escapeClose: config.escapeClose !== false,
            buttons: config.buttons || [],
            onShow: config.onShow || null,
            onHide: config.onHide || null,
            onConfirm: config.onConfirm || null,
            onCancel: config.onCancel || null,
            className: config.className || '',
            style: config.style || {},
            animation: config.animation !== false
        };

        // 生成唯一ID
        const modalId = generateUniqueId('modal');

        // 创建模态框元素
        const modalElement = this.createModalElement(modalConfig);
        
        // 设置z-index
        this.currentZIndex += 10;
        modalElement.style.zIndex = this.currentZIndex;

        // 存储模态框信息
        this.modals.set(modalId, {
            id: modalId,
            element: modalElement,
            config: modalConfig,
            createdAt: Date.now()
        });

        // 添加到DOM
        document.body.appendChild(modalElement);

        // 应用动画
        if (modalConfig.animation) {
            this.animateIn(modalElement);
        }

        // 执行显示回调
        if (modalConfig.onShow) {
            safeExecute(() => modalConfig.onShow(modalId));
        }

        console.log(`🪟 模态框已显示: ${modalId}`);
        return modalId;
    }

    /**
     * 创建模态框元素
     * @SERVICE 模态框元素创建方法
     * @param {Object} config - 模态框配置
     * @returns {HTMLElement} 模态框元素
     */
    createModalElement(config) {
        const modal = document.createElement('div');
        modal.className = `modal-overlay ${config.className}`;
        
        // 基础样式
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: ${config.backdrop ? 'rgba(0, 0, 0, 0.5)' : 'transparent'};
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        // 应用自定义样式
        Object.assign(modal.style, config.style);

        // 创建模态框内容
        const content = this.createModalContent(config);
        modal.appendChild(content);

        // 绑定事件
        this.bindModalEvents(modal, config);

        return modal;
    }

    /**
     * 创建模态框内容
     * @SERVICE 模态框内容创建方法
     * @param {Object} config - 模态框配置
     * @returns {HTMLElement} 模态框内容元素
     */
    createModalContent(config) {
        const content = document.createElement('div');
        content.className = `modal-content modal-${config.size}`;
        
        // 基础样式
        content.style.cssText = `
            background: white;
            border-radius: 8px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-height: 80vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        `;

        // 设置大小
        this.applySizeStyles(content, config.size);

        // 构建内容HTML
        let html = '';

        // 添加头部
        if (config.title || config.closable) {
            html += `
                <div class="modal-header" style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px 24px;
                    border-bottom: 1px solid #e5e7eb;
                ">
                    <h3 class="modal-title" style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">
                        ${config.title}
                    </h3>
                    ${config.closable ? `
                        <button type="button" class="modal-close" style="
                            background: none;
                            border: none;
                            font-size: 24px;
                            cursor: pointer;
                            color: #6b7280;
                            padding: 0;
                            width: 32px;
                            height: 32px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 4px;
                        ">&times;</button>
                    ` : ''}
                </div>
            `;
        }

        // 添加主体内容
        html += `
            <div class="modal-body" style="
                padding: 24px;
                flex: 1;
                overflow-y: auto;
            ">
                ${config.content}
            </div>
        `;

        // 添加底部按钮
        if (config.buttons && config.buttons.length > 0) {
            html += `
                <div class="modal-footer" style="
                    display: flex;
                    justify-content: flex-end;
                    gap: 12px;
                    padding: 16px 24px;
                    border-top: 1px solid #e5e7eb;
                    background: #f9fafb;
                ">
                    ${config.buttons.map(button => `
                        <button type="button" 
                                class="modal-btn modal-btn-${button.type || 'default'}" 
                                data-action="${button.action || ''}"
                                style="
                                    padding: 8px 16px;
                                    border-radius: 6px;
                                    font-size: 14px;
                                    font-weight: 500;
                                    cursor: pointer;
                                    border: 1px solid;
                                    transition: all 0.2s ease;
                                    ${this.getButtonStyles(button.type || 'default')}
                                ">
                            ${button.text}
                        </button>
                    `).join('')}
                </div>
            `;
        }

        content.innerHTML = html;
        return content;
    }

    /**
     * 应用大小样式
     * @UTIL 大小样式应用工具
     * @param {HTMLElement} element - 元素
     * @param {string} size - 大小
     */
    applySizeStyles(element, size) {
        const sizeStyles = {
            [MODAL_SIZES.SMALL]: { width: '400px', maxWidth: '90vw' },
            [MODAL_SIZES.MEDIUM]: { width: '600px', maxWidth: '90vw' },
            [MODAL_SIZES.LARGE]: { width: '800px', maxWidth: '90vw' },
            [MODAL_SIZES.EXTRA_LARGE]: { width: '1000px', maxWidth: '95vw' },
            [MODAL_SIZES.FULL]: { width: '95vw', height: '95vh', maxWidth: 'none' }
        };

        const styles = sizeStyles[size] || sizeStyles[MODAL_SIZES.MEDIUM];
        Object.assign(element.style, styles);
    }

    /**
     * 获取按钮样式
     * @UTIL 按钮样式获取工具
     * @param {string} type - 按钮类型
     * @returns {string} 样式字符串
     */
    getButtonStyles(type) {
        const styles = {
            primary: 'background: #3b82f6; color: white; border-color: #3b82f6;',
            secondary: 'background: #6b7280; color: white; border-color: #6b7280;',
            success: 'background: #10b981; color: white; border-color: #10b981;',
            danger: 'background: #ef4444; color: white; border-color: #ef4444;',
            warning: 'background: #f59e0b; color: white; border-color: #f59e0b;',
            default: 'background: white; color: #374151; border-color: #d1d5db;'
        };
        return styles[type] || styles.default;
    }

    /**
     * 绑定模态框事件
     * @SERVICE 模态框事件绑定方法
     * @param {HTMLElement} modal - 模态框元素
     * @param {Object} config - 模态框配置
     */
    bindModalEvents(modal, config) {
        // 背景点击关闭
        if (config.backdropClose) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    const modalId = this.findModalId(modal);
                    if (modalId) {
                        this.hide(modalId);
                    }
                }
            });
        }

        // 关闭按钮事件
        const closeBtn = modal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                const modalId = this.findModalId(modal);
                if (modalId) {
                    this.hide(modalId);
                }
            });
        }

        // 按钮事件
        const buttons = modal.querySelectorAll('.modal-btn');
        buttons.forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.dataset.action;
                const modalId = this.findModalId(modal);
                
                if (action === 'confirm' && config.onConfirm) {
                    safeExecute(() => config.onConfirm(modalId));
                } else if (action === 'cancel' && config.onCancel) {
                    safeExecute(() => config.onCancel(modalId));
                }

                // 默认关闭模态框
                if (modalId && action !== 'no-close') {
                    this.hide(modalId);
                }
            });
        });
    }

    /**
     * 查找模态框ID
     * @UTIL 模态框ID查找工具
     * @param {HTMLElement} element - 模态框元素
     * @returns {string|null} 模态框ID
     */
    findModalId(element) {
        for (const [id, modal] of this.modals) {
            if (modal.element === element) {
                return id;
            }
        }
        return null;
    }

    /**
     * 隐藏模态框
     * @SERVICE 模态框隐藏方法
     * @param {string} modalId - 模态框ID
     * @returns {boolean} 隐藏是否成功
     */
    hide(modalId) {
        const modal = this.modals.get(modalId);
        if (!modal) {
            return false;
        }

        // 执行隐藏回调
        if (modal.config.onHide) {
            safeExecute(() => modal.config.onHide(modalId));
        }

        // 应用退出动画
        if (modal.config.animation) {
            this.animateOut(modal.element, () => {
                this.removeModal(modalId, modal);
            });
        } else {
            this.removeModal(modalId, modal);
        }

        return true;
    }

    /**
     * 移除模态框
     * @SERVICE 模态框移除方法
     * @param {string} modalId - 模态框ID
     * @param {Object} modal - 模态框对象
     */
    removeModal(modalId, modal) {
        // 从DOM中移除
        if (modal.element.parentNode) {
            modal.element.parentNode.removeChild(modal.element);
        }
        
        // 从存储中移除
        this.modals.delete(modalId);
        
        console.log(`🪟 模态框已隐藏: ${modalId}`);
    }

    /**
     * 入场动画
     * @SERVICE 入场动画方法
     * @param {HTMLElement} element - 模态框元素
     */
    animateIn(element) {
        const content = element.querySelector('.modal-content');
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
            if (content) {
                content.style.transform = 'scale(1)';
            }
        });
    }

    /**
     * 退场动画
     * @SERVICE 退场动画方法
     * @param {HTMLElement} element - 模态框元素
     * @param {Function} callback - 动画完成回调
     */
    animateOut(element, callback) {
        const content = element.querySelector('.modal-content');
        
        element.style.opacity = '0';
        if (content) {
            content.style.transform = 'scale(0.9)';
        }
        
        setTimeout(() => {
            if (callback) callback();
        }, 300);
    }

    /**
     * 关闭顶层模态框
     * @SERVICE 顶层模态框关闭方法
     */
    closeTopModal() {
        if (this.modals.size === 0) return;

        // 找到z-index最高的模态框
        let topModal = null;
        let maxZIndex = 0;

        for (const modal of this.modals.values()) {
            const zIndex = parseInt(modal.element.style.zIndex) || 0;
            if (zIndex > maxZIndex) {
                maxZIndex = zIndex;
                topModal = modal;
            }
        }

        if (topModal && topModal.config.escapeClose) {
            this.hide(topModal.id);
        }
    }

    /**
     * 关闭所有模态框
     * @SERVICE 所有模态框关闭方法
     */
    closeAll() {
        const modalIds = Array.from(this.modals.keys());
        modalIds.forEach(id => this.hide(id));
    }

    /**
     * 获取活跃模态框数量
     * @SERVICE 活跃模态框数量获取方法
     * @returns {number} 活跃模态框数量
     */
    getActiveCount() {
        return this.modals.size;
    }

    /**
     * 销毁模态框管理器
     * @LIFECYCLE 模态框管理器销毁方法
     */
    destroy() {
        this.closeAll();
        this.modals.clear();
        console.log('🗑️ ModalManager 已销毁');
    }

    // ==================== 便捷方法 ====================

    /**
     * 显示警告对话框
     * @SERVICE 警告对话框显示方法
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @param {Function} callback - 回调函数
     * @returns {string} 模态框ID
     */
    alert(title, message, callback) {
        return this.show({
            type: MODAL_TYPES.ALERT,
            title: title,
            content: `<p style="margin: 0; line-height: 1.6;">${message}</p>`,
            size: MODAL_SIZES.SMALL,
            buttons: [
                {
                    text: '确定',
                    type: 'primary',
                    action: 'confirm'
                }
            ],
            onConfirm: callback
        });
    }

    /**
     * 显示确认对话框
     * @SERVICE 确认对话框显示方法
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     * @returns {string} 模态框ID
     */
    confirm(title, message, onConfirm, onCancel) {
        return this.show({
            type: MODAL_TYPES.CONFIRM,
            title: title,
            content: `<p style="margin: 0; line-height: 1.6;">${message}</p>`,
            size: MODAL_SIZES.SMALL,
            buttons: [
                {
                    text: '取消',
                    type: 'default',
                    action: 'cancel'
                },
                {
                    text: '确定',
                    type: 'primary',
                    action: 'confirm'
                }
            ],
            onConfirm: onConfirm,
            onCancel: onCancel
        });
    }

    /**
     * 显示输入对话框
     * @SERVICE 输入对话框显示方法
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @param {string} defaultValue - 默认值
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     * @returns {string} 模态框ID
     */
    prompt(title, message, defaultValue = '', onConfirm, onCancel) {
        const inputId = generateUniqueId('input');

        return this.show({
            type: MODAL_TYPES.PROMPT,
            title: title,
            content: `
                <p style="margin: 0 0 16px 0; line-height: 1.6;">${message}</p>
                <input type="text" id="${inputId}" value="${defaultValue}" style="
                    width: 100%;
                    padding: 8px 12px;
                    border: 1px solid #d1d5db;
                    border-radius: 6px;
                    font-size: 14px;
                    outline: none;
                    transition: border-color 0.2s ease;
                " />
            `,
            size: MODAL_SIZES.SMALL,
            buttons: [
                {
                    text: '取消',
                    type: 'default',
                    action: 'cancel'
                },
                {
                    text: '确定',
                    type: 'primary',
                    action: 'confirm'
                }
            ],
            onShow: () => {
                // 聚焦输入框
                setTimeout(() => {
                    const input = document.getElementById(inputId);
                    if (input) {
                        input.focus();
                        input.select();
                    }
                }, 100);
            },
            onConfirm: () => {
                const input = document.getElementById(inputId);
                const value = input ? input.value : '';
                if (onConfirm) onConfirm(value);
            },
            onCancel: onCancel
        });
    }

    /**
     * 显示加载对话框
     * @SERVICE 加载对话框显示方法
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @returns {string} 模态框ID
     */
    loading(title, message) {
        return this.show({
            type: MODAL_TYPES.CUSTOM,
            title: title,
            content: `
                <div style="text-align: center; padding: 20px 0;">
                    <div style="
                        width: 40px;
                        height: 40px;
                        border: 4px solid #f3f4f6;
                        border-top: 4px solid #3b82f6;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 0 auto 16px auto;
                    "></div>
                    <p style="margin: 0; color: #6b7280;">${message}</p>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `,
            size: MODAL_SIZES.SMALL,
            closable: false,
            backdropClose: false,
            escapeClose: false
        });
    }

    /**
     * 显示进度对话框
     * @SERVICE 进度对话框显示方法
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @param {number} progress - 进度百分比
     * @returns {string} 模态框ID
     */
    progress(title, message, progress = 0) {
        const progressId = generateUniqueId('progress');
        const percentageId = generateUniqueId('percentage');
        const messageId = generateUniqueId('message');

        return this.show({
            type: MODAL_TYPES.CUSTOM,
            title: title,
            content: `
                <div style="padding: 20px 0;">
                    <p id="${messageId}" style="margin: 0 0 16px 0; text-align: center;">${message}</p>
                    <div style="
                        width: 100%;
                        height: 8px;
                        background: #f3f4f6;
                        border-radius: 4px;
                        overflow: hidden;
                        margin-bottom: 8px;
                    ">
                        <div id="${progressId}" style="
                            height: 100%;
                            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
                            width: ${Math.max(0, Math.min(100, progress))}%;
                            transition: width 0.3s ease;
                        "></div>
                    </div>
                    <div style="text-align: center;">
                        <span id="${percentageId}" style="font-size: 14px; color: #6b7280;">
                            ${Math.round(progress)}%
                        </span>
                    </div>
                </div>
            `,
            size: MODAL_SIZES.MEDIUM,
            closable: false,
            backdropClose: false,
            escapeClose: false,
            buttons: [
                {
                    text: '取消',
                    type: 'default',
                    action: 'cancel'
                }
            ]
        });
    }

    /**
     * 更新进度对话框
     * @SERVICE 进度对话框更新方法
     * @param {string} modalId - 模态框ID
     * @param {string} message - 新消息
     * @param {number} progress - 新进度百分比
     */
    updateProgress(modalId, message, progress) {
        const modal = this.modals.get(modalId);
        if (!modal) return;

        const progressBar = modal.element.querySelector('[id*="progress"]');
        const percentageSpan = modal.element.querySelector('[id*="percentage"]');
        const messageP = modal.element.querySelector('[id*="message"]');

        if (progressBar) {
            progressBar.style.width = `${Math.max(0, Math.min(100, progress))}%`;
        }

        if (percentageSpan) {
            percentageSpan.textContent = `${Math.round(progress)}%`;
        }

        if (messageP && message) {
            messageP.textContent = message;
        }
    }
}

// ==================== 全局模态框管理器实例 ====================
let globalModalManager = null;

/**
 * 获取全局模态框管理器实例
 * @SERVICE 全局模态框管理器获取函数
 * @returns {ModalManager} 模态框管理器实例
 */
function getModalManager() {
    if (!globalModalManager) {
        globalModalManager = new ModalManager();
    }
    return globalModalManager;
}

// ==================== 便捷函数 ====================

/**
 * 显示模态框
 * @SERVICE 模态框显示函数
 * @param {Object} config - 模态框配置
 * @returns {string} 模态框ID
 */
function showModal(config) {
    return getModalManager().show(config);
}

/**
 * 隐藏模态框
 * @SERVICE 模态框隐藏函数
 * @param {string} modalId - 模态框ID
 * @returns {boolean} 隐藏是否成功
 */
function hideModal(modalId) {
    return getModalManager().hide(modalId);
}

/**
 * 显示警告对话框
 * @SERVICE 警告对话框显示函数
 * @param {string} title - 标题
 * @param {string} message - 消息
 * @param {Function} callback - 回调函数
 * @returns {string} 模态框ID
 */
function showAlert(title, message, callback) {
    return getModalManager().alert(title, message, callback);
}

/**
 * 显示确认对话框
 * @SERVICE 确认对话框显示函数
 * @param {string} title - 标题
 * @param {string} message - 消息
 * @param {Function} onConfirm - 确认回调
 * @param {Function} onCancel - 取消回调
 * @returns {string} 模态框ID
 */
function showConfirm(title, message, onConfirm, onCancel) {
    return getModalManager().confirm(title, message, onConfirm, onCancel);
}

/**
 * 显示输入对话框
 * @SERVICE 输入对话框显示函数
 * @param {string} title - 标题
 * @param {string} message - 消息
 * @param {string} defaultValue - 默认值
 * @param {Function} onConfirm - 确认回调
 * @param {Function} onCancel - 取消回调
 * @returns {string} 模态框ID
 */
function showPrompt(title, message, defaultValue, onConfirm, onCancel) {
    return getModalManager().prompt(title, message, defaultValue, onConfirm, onCancel);
}

/**
 * 显示加载对话框
 * @SERVICE 加载对话框显示函数
 * @param {string} title - 标题
 * @param {string} message - 消息
 * @returns {string} 模态框ID
 */
function showLoading(title, message) {
    return getModalManager().loading(title, message);
}

/**
 * 显示进度对话框
 * @SERVICE 进度对话框显示函数
 * @param {string} title - 标题
 * @param {string} message - 消息
 * @param {number} progress - 进度百分比
 * @returns {string} 模态框ID
 */
function showProgress(title, message, progress) {
    return getModalManager().progress(title, message, progress);
}

/**
 * 更新进度对话框
 * @SERVICE 进度对话框更新函数
 * @param {string} modalId - 模态框ID
 * @param {string} message - 新消息
 * @param {number} progress - 新进度百分比
 */
function updateProgress(modalId, message, progress) {
    getModalManager().updateProgress(modalId, message, progress);
}

/**
 * 关闭所有模态框
 * @SERVICE 所有模态框关闭函数
 */
function closeAllModals() {
    getModalManager().closeAll();
}

// ==================== 兼容性函数 ====================

/**
 * 显示优化进度对话框（兼容旧版本）
 * @SERVICE 优化进度对话框显示函数
 * @returns {string} 模态框ID
 */
function showOptimizationProgressDialog() {
    return showProgress('智能优化进行中', '准备中...', 0);
}

/**
 * 隐藏优化进度对话框（兼容旧版本）
 * @SERVICE 优化进度对话框隐藏函数
 */
function hideOptimizationProgressDialog() {
    // 查找优化进度对话框并关闭
    const manager = getModalManager();
    for (const [modalId, modal] of manager.modals) {
        if (modal.config.title === '智能优化进行中') {
            manager.hide(modalId);
            break;
        }
    }
}

/**
 * 更新优化进度（兼容旧版本）
 * @SERVICE 优化进度更新函数
 * @param {Object} progress - 进度信息
 */
function updateOptimizationProgress(progress) {
    const manager = getModalManager();
    for (const [modalId, modal] of manager.modals) {
        if (modal.config.title === '智能优化进行中') {
            manager.updateProgress(modalId, progress.step || '处理中...', progress.percentage || 0);
            break;
        }
    }
}

/**
 * 显示优化结果对话框（兼容旧版本）
 * @SERVICE 优化结果对话框显示函数
 * @param {Object} result - 优化结果
 * @returns {string} 模态框ID
 */
function showOptimizationResultDialog(result) {
    const content = `
        <div style="text-align: center; margin-bottom: 20px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div style="padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    <div style="font-size: 24px; font-weight: bold; color: #007bff;">${result.original.count}</div>
                    <div style="font-size: 14px; color: #666;">原始数量</div>
                </div>
                <div style="padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    <div style="font-size: 24px; font-weight: bold; color: #28a745;">${result.optimized.count}</div>
                    <div style="font-size: 14px; color: #666;">优化后数量</div>
                </div>
            </div>
            <div style="background: #e8f5e8; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span>减少数量:</span>
                    <span style="font-weight: bold; color: #28a745;">${result.statistics.duplicatesRemoved || 0} 条</span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                    <span>优化率:</span>
                    <span style="font-weight: bold; color: #28a745;">${result.statistics.reductionRate || 0}%</span>
                </div>
            </div>
            <div style="text-align: left; font-size: 14px; color: #666; line-height: 1.6;">
                <strong>优化效果:</strong><br>
                • 移除了重复和相似的问答条目<br>
                • 保留了最高质量的答案<br>
                • 保持了信息完整性和准确性
            </div>
        </div>
    `;

    return showModal({
        title: '优化完成',
        content: content,
        size: MODAL_SIZES.MEDIUM,
        buttons: [
            {
                text: '确定',
                type: 'primary',
                action: 'confirm'
            }
        ]
    });
}

/**
 * 显示文件选择对话框（兼容旧版本）
 * @SERVICE 文件选择对话框显示函数
 * @param {Array} files - 文件列表
 * @param {Function} onConfirm - 确认回调
 * @returns {string} 模态框ID
 */
function showFileSelectionDialog(files = [], onConfirm) {
    if (files.length === 0) {
        showAlert('提示', '没有已完成分析的文件');
        return null;
    }

    const content = `
        <div style="margin-bottom: 20px;">
            <p style="margin: 0 0 16px 0; color: #666;">请选择要包含在报告中的文件：</p>
            <div class="file-selection-list" style="max-height: 300px; overflow-y: auto; border: 1px solid #e5e7eb; border-radius: 6px;">
                ${files.map(file => `
                    <label class="file-selection-item" style="
                        display: flex;
                        align-items: center;
                        padding: 12px;
                        border-bottom: 1px solid #f3f4f6;
                        cursor: pointer;
                        transition: background-color 0.2s ease;
                    " onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                        <input type="checkbox" value="${file.id}" checked style="margin-right: 12px;">
                        <div style="flex: 1;">
                            <div class="file-name" style="font-weight: 500; margin-bottom: 4px;">${file.name}</div>
                            <div class="file-meta" style="font-size: 12px; color: #6b7280;">
                                ${new Date(file.completedAt || Date.now()).toLocaleDateString()}
                            </div>
                        </div>
                    </label>
                `).join('')}
            </div>
        </div>
    `;

    return showModal({
        title: '选择文件',
        content: content,
        size: MODAL_SIZES.LARGE,
        buttons: [
            {
                text: '取消',
                type: 'default',
                action: 'cancel'
            },
            {
                text: '确认选择',
                type: 'primary',
                action: 'confirm'
            }
        ],
        onConfirm: (modalId) => {
            const modal = getModalManager().modals.get(modalId);
            if (modal) {
                const checkboxes = modal.element.querySelectorAll('.file-selection-list input[type="checkbox"]:checked');
                const selectedFiles = Array.from(checkboxes).map(cb => cb.value);
                if (onConfirm) onConfirm(selectedFiles);
            }
        }
    });
}

/**
 * 显示文件详情对话框（兼容旧版本）
 * @SERVICE 文件详情对话框显示函数
 * @param {Object} file - 文件信息
 * @returns {string} 模态框ID
 */
function showFileDetailsDialog(file) {
    const content = `
        <div class="file-detail-grid" style="display: grid; gap: 16px;">
            <div class="detail-item" style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f3f4f6;">
                <label style="font-weight: 500; color: #374151;">文件名:</label>
                <span style="color: #6b7280;">${file.fileName || file.name}</span>
            </div>
            <div class="detail-item" style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f3f4f6;">
                <label style="font-weight: 500; color: #374151;">文件大小:</label>
                <span style="color: #6b7280;">${formatFileSize(file.fileSize || file.size)}</span>
            </div>
            <div class="detail-item" style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f3f4f6;">
                <label style="font-weight: 500; color: #374151;">上传时间:</label>
                <span style="color: #6b7280;">${formatDateTime(file.uploadTime || file.createdAt)}</span>
            </div>
            <div class="detail-item" style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f3f4f6;">
                <label style="font-weight: 500; color: #374151;">处理状态:</label>
                <span style="color: ${getStatusColor(file.processingStatus || file.status)};">
                    ${getStatusText(file.processingStatus || file.status)}
                </span>
            </div>
            ${file.processingResult ? `
                <div class="detail-item" style="padding: 8px 0;">
                    <label style="font-weight: 500; color: #374151; display: block; margin-bottom: 8px;">处理结果:</label>
                    <div style="background: #f9fafb; padding: 12px; border-radius: 6px; font-size: 14px; color: #6b7280;">
                        ${JSON.stringify(file.processingResult, null, 2)}
                    </div>
                </div>
            ` : ''}
        </div>
    `;

    return showModal({
        title: '文件详情',
        content: content,
        size: MODAL_SIZES.MEDIUM,
        buttons: [
            {
                text: '关闭',
                type: 'default',
                action: 'confirm'
            }
        ]
    });
}

// ==================== 辅助函数 ====================

/**
 * 格式化文件大小
 * @UTIL 文件大小格式化工具
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
function formatFileSize(bytes) {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化日期时间
 * @UTIL 日期时间格式化工具
 * @param {number|string|Date} timestamp - 时间戳
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(timestamp) {
    if (!timestamp) return '未知';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN');
}

/**
 * 获取状态颜色
 * @UTIL 状态颜色获取工具
 * @param {string} status - 状态
 * @returns {string} 颜色值
 */
function getStatusColor(status) {
    const colors = {
        'completed': '#10b981',
        'processing': '#3b82f6',
        'failed': '#ef4444',
        'pending': '#f59e0b'
    };
    return colors[status] || '#6b7280';
}

/**
 * 获取状态文本
 * @UTIL 状态文本获取工具
 * @param {string} status - 状态
 * @returns {string} 状态文本
 */
function getStatusText(status) {
    const texts = {
        'completed': '已完成',
        'processing': '处理中',
        'failed': '失败',
        'pending': '等待中'
    };
    return texts[status] || '未知';
}

// 模块导出到全局
window.ModuleExports = window.ModuleExports || {};
window.ModuleExports['modal.js'] = { ModalManager, getModalManager };
