/**
 * 本地配置文件模板
 * 
 * 使用说明：
 * 1. 复制此文件为 local-config.js
 * 2. 填入您的API密钥和个人配置
 * 3. local-config.js 不会被版本控制系统跟踪
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

window.LOCAL_CONFIG = {
    // ==================== API配置 ====================
    
    /**
     * Kimi API密钥
     * 获取方式：访问 https://platform.moonshot.cn/ 注册并获取API Key
     * 格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
     */
    apiKey: 'sk-your-kimi-api-key-here',
    
    /**
     * API基础URL (通常不需要修改)
     */
    apiBaseUrl: 'https://api.moonshot.cn/v1',
    
    /**
     * 使用的模型 (可选配置)
     * 可选值: 'kimi-k2-turbo-preview', 'kimi-k2-plus'
     */
    model: 'kimi-k2-turbo-preview',
    
    // ==================== 性能配置 ====================
    
    /**
     * 最大并发请求数 (1-50)
     * 建议根据您的API配额调整
     */
    maxConcurrency: 50,
    
    /**
     * 请求超时时间 (毫秒)
     * 建议范围: 30000-120000 (30秒-2分钟)
     */
    requestTimeout: 90000,
    
    /**
     * 重试次数 (0-5)
     */
    retryAttempts: 3,
    
    // ==================== 用户偏好配置 ====================
    
    /**
     * 界面主题
     * 可选值: 'light', 'dark', 'auto'
     */
    theme: 'light',
    
    /**
     * 语言设置
     * 可选值: 'zh-CN', 'en-US'
     */
    language: 'zh-CN',
    
    /**
     * 自动保存间隔 (毫秒)
     * 0 表示禁用自动保存
     */
    autoSaveInterval: 30000,
    
    /**
     * 分页大小
     * 建议范围: 10-100
     */
    pageSize: 20,
    
    // ==================== 高级配置 ====================
    
    /**
     * 启用调试模式
     * 开启后会在控制台显示详细日志
     */
    debug: false,
    
    /**
     * 启用性能监控
     * 开启后会记录性能指标
     */
    performanceMonitoring: false,
    
    /**
     * 数据缓存时间 (毫秒)
     * 0 表示不缓存
     */
    cacheTimeout: 300000, // 5分钟
    
    /**
     * 自定义分析提示词
     * 可以根据需要自定义AI分析的提示词
     */
    customPrompts: {
        effectiveness: '请分析这段对话中客服回复的有效性，评分1-5分',
        satisfaction: '请分析这段对话中客户的满意度，评分1-5分',
        category: '请为这段对话的问题进行分类'
    },
    
    // ==================== 导出配置 ====================
    
    /**
     * 默认导出格式
     * 可选值: 'csv', 'json', 'xlsx'
     */
    defaultExportFormat: 'csv',
    
    /**
     * 导出文件名前缀
     */
    exportFilePrefix: 'analysis_result',
    
    /**
     * 是否在导出时包含原始数据
     */
    includeRawData: true,
    
    // ==================== 通知配置 ====================
    
    /**
     * 启用桌面通知
     * 需要用户授权
     */
    enableNotifications: true,
    
    /**
     * 启用声音提示
     */
    enableSounds: false,
    
    /**
     * 消息显示时间 (毫秒)
     */
    messageDisplayTime: 3000
};

// ==================== 配置验证函数 ====================

/**
 * 验证配置的有效性
 */
function validateConfig() {
    const config = window.LOCAL_CONFIG;
    const errors = [];
    
    // 验证API密钥
    if (!config.apiKey || config.apiKey === 'sk-your-kimi-api-key-here') {
        errors.push('请设置有效的Kimi API密钥');
    }
    
    // 验证并发数
    if (config.maxConcurrency < 1 || config.maxConcurrency > 50) {
        errors.push('最大并发数应在1-50之间');
    }
    
    // 验证超时时间
    if (config.requestTimeout < 10000 || config.requestTimeout > 300000) {
        errors.push('请求超时时间应在10秒-5分钟之间');
    }
    
    if (errors.length > 0) {
        console.warn('配置验证失败:', errors);
        return false;
    }
    
    return true;
}

// ==================== 配置初始化 ====================

/**
 * 初始化配置
 */
function initConfig() {
    // 验证配置
    if (!validateConfig()) {
        console.warn('配置验证失败，将使用默认配置');
    }
    
    // 合并默认配置
    const defaultConfig = window.CONSTANTS || {};
    window.LOCAL_CONFIG = Object.assign({}, defaultConfig, window.LOCAL_CONFIG);
    
    console.log('本地配置已加载');
}

// 自动初始化
if (typeof window !== 'undefined') {
    initConfig();
}
