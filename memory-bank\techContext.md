# 💻 技术上下文 - 技术栈和开发环境

## 🛠️ 核心技术栈 [RS:5]

### 前端技术
- **HTML5**: 语义化标记，现代Web标准
- **CSS3**: 响应式设计，CSS变量系统
- **JavaScript ES6+**: 模块化、异步处理、现代语法
- **ES6 Modules**: 标准化模块系统，动态导入

### 外部依赖库 [RS:4]
```javascript
// CDN依赖 - 零npm安装
const EXTERNAL_LIBRARIES = {
    // 图表渲染引擎
    echarts: 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js',
    // 备选: Plotly.js (旧代码使用)
    plotly: 'https://cdn.plot.ly/plotly-2.24.1.min.js',
    // CSV处理库
    papaparse: 'https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js'
};
```

### AI服务集成 [RS:4]
```javascript
// Kimi API配置
const AI_CONFIG = {
    endpoint: 'https://api.moonshot.cn/v1/chat/completions',
    model: 'kimi-k2-turbo-preview',
    timeout: 90000, // 90秒超时
    maxRetries: 3,
    retryDelay: 1000
};
```

## 🏗️ 架构技术决策 [RS:5]

### 模块系统选择
**决策**: 使用ES6 Modules + 动态加载
**原因**: 
- 标准化，未来兼容性好
- 支持静态分析和tree-shaking
- 避免CommonJS/AMD的复杂性

**实现挑战**: file://协议CORS限制
**解决方案**: 动态脚本注入 + 模块转换

### 图表库选择 [RS:4]
**当前状态**: 存在不一致
- 文档规划: ECharts 5.x
- 旧代码实现: Plotly.js 2.24.1

**决策待定**: 需要在Phase 2中确定
**评估标准**:
- 功能完整性
- 性能表现
- 文件大小
- 学习曲线

### 构建工具策略 [RS:5]
**决策**: 零构建工具
**原因**:
- 简化部署流程
- 降低用户门槛
- 避免构建复杂性
- 支持file://协议

**实现方式**:
- 原生ES6模块
- CDN外部依赖
- 动态模块加载

## 🌐 浏览器兼容性 [RS:3]

### 目标浏览器支持
```javascript
const BROWSER_SUPPORT = {
    Chrome: '88+',    // ES6 Modules稳定支持
    Firefox: '78+',   // ES6 Modules稳定支持
    Safari: '14+',    // ES6 Modules稳定支持
    Edge: '88+',      // Chromium-based Edge
    // 不支持IE11及以下版本
};
```

### 特性检测
```javascript
// 浏览器能力检测
const FEATURE_DETECTION = {
    esModules: 'noModule' in HTMLScriptElement.prototype,
    fetch: typeof fetch !== 'undefined',
    localStorage: typeof Storage !== 'undefined',
    fileAPI: typeof FileReader !== 'undefined'
};
```

## 📁 文件系统约束 [RS:4]

### 协议支持
- **file://**: 主要目标，本地文件直接运行
- **http://**: 开发环境支持
- **https://**: 生产环境支持

### 路径解析策略
```javascript
// 相对路径解析
const MODULE_BASE_PATH = './src/';
const CONFIG_BASE_PATH = './config/';

// 动态路径构建
function resolveModulePath(moduleName) {
    return `${MODULE_BASE_PATH}${moduleName}`;
}
```

## 🔧 开发环境设置 [RS:3]

### 推荐开发工具
- **代码编辑器**: VS Code
- **浏览器**: Chrome DevTools
- **版本控制**: Git
- **本地服务器**: Python http.server (可选)

### 开发工作流
```bash
# 1. 克隆项目
git clone <repository>

# 2. 配置API密钥
cp config/local-config.example.js config/local-config.js
# 编辑local-config.js添加Kimi API Key

# 3. 直接运行
# 方法1: 双击index.html
# 方法2: 本地服务器
python -m http.server 8000
```

## 📊 性能技术规格 [RS:3]

### 文件大小限制
```javascript
const FILE_SIZE_LIMITS = {
    'index.html': '1KB',
    'loader.js': '3KB',
    'constants.js': '1KB',
    'storage.js': '8KB',
    'parser.js': '12KB',
    'charts.js': '6KB',
    'drag-upload.js': '4KB',
    'main.js': '8KB'
};
```

### 性能目标
```javascript
const PERFORMANCE_TARGETS = {
    moduleLoadTime: 2000,      // 模块加载 < 2秒
    fileParseTime: 5000,       // 文件解析 < 5秒
    aiAnalysisTime: 90000,     // AI分析 < 90秒
    chartRenderTime: 1000,     // 图表渲染 < 1秒
    memoryUsage: 500 * 1024 * 1024  // 内存使用 < 500MB
};
```

## 🔐 安全技术配置 [RS:3]

### API密钥管理
```javascript
// 配置文件模式
// config/local-config.js (不入版本控制)
window.LOCAL_CONFIG = {
    apiKey: 'sk-your-kimi-api-key-here',
    // 其他敏感配置
};
```

### 数据安全策略
- **本地处理**: 所有数据在浏览器本地处理
- **无数据上传**: 除AI API调用外，不向外部发送数据
- **localStorage加密**: 敏感数据可选加密存储

## 🧪 测试技术栈 [RS:2]

### 测试策略
- **手动测试**: 主要测试方式
- **浏览器DevTools**: 性能和错误监控
- **真实数据测试**: 使用New folder/中的数据

### 测试环境
```javascript
const TEST_ENVIRONMENTS = {
    local: 'file://协议直接运行',
    development: 'http://localhost:8000',
    staging: 'https://test-domain.com',
    production: '用户本地环境'
};
```

## 📦 部署技术要求 [RS:4]

### 零依赖部署
- **无需安装**: 不需要Node.js、npm等
- **无需构建**: 直接使用源代码
- **无需服务器**: 支持本地文件运行

### 分发策略
```
分发包结构:
├── index.html
├── loader.js
├── src/
│   ├── *.js (所有模块文件)
├── config/
│   └── local-config.example.js
└── docs/
    └── README.md
```

## 🔄 版本控制策略 [RS:2]

### Git配置
```gitignore
# .gitignore
config/local-config.js    # 用户API密钥
*.log                      # 日志文件
.DS_Store                  # macOS系统文件
Thumbs.db                  # Windows系统文件
```

### 分支策略
- **main**: 稳定版本
- **develop**: 开发版本
- **feature/***: 功能分支
- **hotfix/***: 紧急修复

## 🚀 未来技术演进 [RS:1]

### 短期技术升级
- HTTP/2 Push优化
- Service Worker缓存
- WebAssembly性能优化

### 长期技术规划
- Progressive Web App支持
- 离线功能增强
- 多语言国际化支持
