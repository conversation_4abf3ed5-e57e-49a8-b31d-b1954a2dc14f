# IStorageService 接口说明 (Stable Draft)

> 目标：统一应用数据持久层访问方式，隔离实现细节（localStorage / 将来可替换 IndexedDB / 远端 API）。

## 版本

- Draft: 0.1 (2025-08-14)

## 设计原则

- 单一职责：只做数据持久化与读取格式化，不做业务聚合/复杂统计。
- 稳定契约：对上层（main / 处理器 / 报告）暴露固定方法集合。
- 可替换：内部实现可独立重写，不影响调用端。
- 无 this 依赖：方法为纯函数式风格（对象方法内部不依赖动态 this 绑定）。

## 核心方法 (所有方法返回 Promise)

| 方法 | 参数 | 返回 (Promise<...>) | 说明 |
|------|------|-------------------|------|
| loadAnalysisResults() | - | Promise<`Array`> | 读取所有分析结果数组（可能为空） |
| saveAnalysisResults(result) | result:`Object` | Promise<`Array`> | 追加单条分析结果，返回最新全集 |
| loadQADataset() | - | Promise<`Array`> | 读取 QA 数据集 |
| saveQADataset(data) | data:`Array` | Promise<`Boolean`> | 保存 QA 数据集 |
| loadKnowledgeData() | - | Promise<`Array`> | 读取知识库数据 |
| saveKnowledgeData(data) | data:`Array` | Promise<`Boolean`> | 保存知识库 |
| loadDrivers() | - | Promise<`Array`> | 读取司机/主体数据 |
| saveDrivers(data) | data:`Array` | Promise<`Boolean`> | 保存司机数据 |
| getStorageInfo() | - | Promise<`Object`> | 返回 { available, totalSize, itemSizes } |
| exportAnalysisResultsCSV(filename?) | filename?:String | Promise<String\|null> | 生成并触发下载 CSV，返回CSV文本或 null |

## 数据结构约定 (部分)

- 分析结果对象 (AnalysisResult)

```json
{
  "id": "analysis_1732342342342",
  "timestamp": 1732342342342,
  "fileName": "xxx.txt",
  "stats": {
    "totalConversations": 12,
    "totalMessages": 340,
    "avgEffectiveness": 0.82,
    "avgSatisfaction": 0.90,
    "topCategories": ["投诉","咨询"]
  },
  "conversations": [ /* 原始或处理后对话集合 */ ]
}
```

## 错误与健壮性

- 读取：解析失败返回默认值，不抛异常（防止局部损坏导致崩溃）。
- 保存：JSON.stringify 失败返回 false，并打印警告。
- 导出：无数据时返回 null 并 log warn。

## 迁移策略（已完成阶段汇总）

已完成：Phase1~Phase4（抽象引入 -> 调用方切换 -> 移除旧类 -> 统一异步接口）。
待选（可选后续）：ES Module 化；事件/事务扩展。

## 验收标准 Checklist

- [x] main.js 获取 storage 时优先 storageService。
- [x] 没有新调用依赖 appDataManager 实例特性。
- [x] 允许在不改 main.js 情况下替换实现（仅改 storage-service.js）。
- [x] CSV 导出统一走 exportAnalysisResultsCSV。
- [x] 提供最小接口文档（本文件）。

## 后续扩展建议

- 增加 namespaced API：storageService.analysis / qa / knowledge 分组。
- 引入事件机制：onChange(key, callback)。
- 提供批量事务：batch(mutationsFn)。
- 统一异步化（已完成，返回 Promise）。

## 使用示例

```js
const svc = window.ModuleExports['storage-service.js'].storageService;
const results = await svc.loadAnalysisResults();
await svc.saveAnalysisResults({ fileName:'demo.txt', stats:{ totalConversations:0 } });
const info = await svc.getStorageInfo();
await svc.exportAnalysisResultsCSV();
```

---

(End)
