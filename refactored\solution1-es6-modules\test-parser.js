/**
 * 文本解析功能测试脚本
 * <AUTHOR> Agent
 * @version 1.0.0
 */

// 测试数据样本
const testData = `
-----------------------------------------------
Time: 02/01/2025 13:51:11
User: Driver: Tan<PERSON>ock<PERSON>oo GMH
Message: 这张单是不是取消了？

-----------------------------------------------

-----------------------------------------------
Time: 02/01/2025 14:00:31
User: Support: GMH Ashley
Message: 请问订单号是 ？

-----------------------------------------------

-----------------------------------------------
Time: 02/01/2025 14:01:20
User: Driver: <PERSON><PERSON><PERSON><PERSON><PERSON> GMH
Message: 你们都取消了那里看到单号哦。

-----------------------------------------------

-----------------------------------------------
Time: 02/01/2025 14:02:14
User: Support: GMH Ashley
Message: 可以知道是几点 哪里去哪里的吗？因为不是我取消的 我不清楚哦

-----------------------------------------------

-----------------------------------------------
Time: 02/01/2025 14:03:02
User: Driver: TanHockJoo GMH
Message: file:2025-1-2_14_02_59-67762c13f2711.jpg_____https://storage.googleapis.com/download/storage/v1/b/gomyhire-379411.appspot.com/o/images%2F2025-1-2_14_02_59-67762c13f2711.jpg?generation=1735797780516581&alt=media

-----------------------------------------------

-----------------------------------------------
Time: 02/01/2025 14:03:05
User: Driver: TanHockJoo GMH
Message: 是不是这张？

-----------------------------------------------

-----------------------------------------------
Time: 02/01/2025 14:07:25
User: Support: GMH Ashley
Message: 对的 我检查到了 顾客取消订单

-----------------------------------------------
`;

/**
 * 运行解析测试
 */
function runParserTests() {
    console.log('=== 开始文本解析功能测试 ===');
    
    // 检查解析器是否可用
    if (!window.ModuleExports || !window.ModuleExports['parser.js']) {
        console.error('❌ 解析器模块未加载');
        return false;
    }
    
    const parser = window.ModuleExports['parser.js'];
    let testsPassed = 0;
    let totalTests = 0;
    
    // 测试1: 基本解析功能
    totalTests++;
    try {
        console.log('\n--- 测试1: 基本解析功能 ---');
        const result = parser.parseTxtContent(testData, 'test-file.txt');
        
        console.log('解析结果:', result);
        
        if (result && result.conversations && result.conversations.length > 0) {
            console.log('✅ 基本解析功能: PASS');
            console.log(`   - 解析出 ${result.conversations.length} 个对话块`);
            console.log(`   - 总消息数: ${result.stats.totalMessages}`);
            testsPassed++;
        } else {
            console.log('❌ 基本解析功能: FAIL - 未解析出对话数据');
        }
    } catch (error) {
        console.log(`❌ 基本解析功能: ERROR - ${error.message}`);
    }
    
    // 测试2: 时间戳解析
    totalTests++;
    try {
        console.log('\n--- 测试2: 时间戳解析 ---');
        const timestamp = parser.parseTimestamp('02/01/2025', '13:51:11');
        
        if (timestamp > 0) {
            console.log('✅ 时间戳解析: PASS');
            console.log(`   - 解析结果: ${new Date(timestamp).toLocaleString()}`);
            testsPassed++;
        } else {
            console.log('❌ 时间戳解析: FAIL - 时间戳为0');
        }
    } catch (error) {
        console.log(`❌ 时间戳解析: ERROR - ${error.message}`);
    }
    
    // 测试3: 数据验证
    totalTests++;
    try {
        console.log('\n--- 测试3: 数据验证 ---');
        const result = parser.parseTxtContent(testData, 'test-file.txt');
        const validation = parser.validateParseResult(result);
        
        console.log('验证结果:', validation);
        
        if (validation.isValid) {
            console.log('✅ 数据验证: PASS');
            console.log(`   - 有效对话数: ${validation.stats.validConversations}`);
            testsPassed++;
        } else {
            console.log('❌ 数据验证: FAIL');
            console.log(`   - 错误: ${validation.errors.join(', ')}`);
        }
    } catch (error) {
        console.log(`❌ 数据验证: ERROR - ${error.message}`);
    }
    
    // 测试4: 角色识别
    totalTests++;
    try {
        console.log('\n--- 测试4: 角色识别 ---');
        const result = parser.parseTxtContent(testData, 'test-file.txt');
        
        let driverCount = 0;
        let supportCount = 0;
        
        result.conversations.forEach(conv => {
            if (conv.role === 'driver') driverCount++;
            if (conv.role === 'support') supportCount++;
        });
        
        if (driverCount > 0 && supportCount > 0) {
            console.log('✅ 角色识别: PASS');
            console.log(`   - 司机对话: ${driverCount}`);
            console.log(`   - 客服对话: ${supportCount}`);
            testsPassed++;
        } else {
            console.log('❌ 角色识别: FAIL - 未正确识别角色');
        }
    } catch (error) {
        console.log(`❌ 角色识别: ERROR - ${error.message}`);
    }
    
    // 测试5: 批量处理
    totalTests++;
    try {
        console.log('\n--- 测试5: 批量处理 ---');
        
        // 创建模拟文件对象
        const mockFile = {
            name: 'test-batch.txt',
            size: testData.length,
            type: 'text/plain'
        };
        
        // 模拟FileReader
        const originalFileReader = window.FileReader;
        window.FileReader = function() {
            this.readAsText = function() {
                setTimeout(() => {
                    this.result = testData;
                    this.onload();
                }, 10);
            };
        };
        
        const batchResult = await parser.parseBatchFiles([mockFile]);
        
        // 恢复原始FileReader
        window.FileReader = originalFileReader;
        
        if (batchResult && batchResult.results && batchResult.results.length > 0) {
            console.log('✅ 批量处理: PASS');
            console.log(`   - 处理文件数: ${batchResult.summary.totalFiles}`);
            console.log(`   - 成功数: ${batchResult.summary.successCount}`);
            testsPassed++;
        } else {
            console.log('❌ 批量处理: FAIL - 批量处理失败');
        }
    } catch (error) {
        console.log(`❌ 批量处理: ERROR - ${error.message}`);
    }
    
    // 输出测试总结
    console.log('\n=== 测试总结 ===');
    console.log(`通过: ${testsPassed}/${totalTests}`);
    console.log(`成功率: ${Math.round((testsPassed / totalTests) * 100)}%`);
    
    return testsPassed === totalTests;
}

/**
 * 显示测试结果到页面
 */
function displayTestResults() {
    const testResultsElement = document.getElementById('test-results');
    if (!testResultsElement) return;
    
    // 重定向console.log到页面显示
    const originalLog = console.log;
    let logOutput = '';
    
    console.log = function(...args) {
        logOutput += args.join(' ') + '\n';
        originalLog.apply(console, args);
    };
    
    // 运行测试
    const success = runParserTests();
    
    // 恢复console.log
    console.log = originalLog;
    
    // 显示结果
    testResultsElement.textContent = logOutput;
    
    return success;
}

// 导出测试函数
window.runParserTests = runParserTests;
window.displayTestResults = displayTestResults;

console.log('✅ 解析器测试脚本已加载');
