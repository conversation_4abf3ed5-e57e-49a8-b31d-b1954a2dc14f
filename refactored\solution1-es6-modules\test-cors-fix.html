<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS修复测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .test-result { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
        }
        .success { 
            background-color: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .error { 
            background-color: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb; 
        }
        .info { 
            background-color: #d1ecf1; 
            color: #0c5460; 
            border: 1px solid #bee5eb; 
        }
        #loading { 
            text-align: center; 
            padding: 20px; 
        }
    </style>
</head>
<body>
    <h1>CORS修复测试页面</h1>
    
    <div id="loading">
        <p>正在测试模块加载...</p>
        <div id="loading-progress"></div>
    </div>
    
    <div id="test-results" style="display: none;">
        <h2>测试结果</h2>
        <div id="test-output"></div>
        
        <h3>模块加载状态</h3>
        <div id="module-status"></div>
        
        <h3>功能测试</h3>
        <button onclick="testUtilityFunctions()">测试工具函数</button>
        <button onclick="testConstants()">测试常量</button>
        <div id="function-test-results"></div>
    </div>

    <!-- 测试脚本 -->
    <script>
        let testResults = [];
        
        function addTestResult(message, type = 'info') {
            testResults.push({ message, type, timestamp: new Date() });
            updateTestOutput();
        }
        
        function updateTestOutput() {
            const output = document.getElementById('test-output');
            output.innerHTML = testResults.map(result => 
                `<div class="test-result ${result.type}">
                    [${result.timestamp.toLocaleTimeString()}] ${result.message}
                </div>`
            ).join('');
        }
        
        function updateModuleStatus() {
            const statusDiv = document.getElementById('module-status');
            const moduleExports = window.ModuleExports || {};
            const constants = window.CONSTANTS;
            
            let statusHTML = '';
            
            // 检查constants
            if (constants) {
                statusHTML += '<div class="test-result success">✅ constants.js - 已加载</div>';
            } else {
                statusHTML += '<div class="test-result error">❌ constants.js - 未加载</div>';
            }
            
            // 检查utils
            if (moduleExports['utils.js']) {
                statusHTML += '<div class="test-result success">✅ utils.js - 已加载</div>';
            } else {
                statusHTML += '<div class="test-result error">❌ utils.js - 未加载</div>';
            }
            
            statusDiv.innerHTML = statusHTML;
        }
        
        function testUtilityFunctions() {
            const results = document.getElementById('function-test-results');
            const utils = window.ModuleExports && window.ModuleExports['utils.js'];
            
            if (!utils) {
                results.innerHTML = '<div class="test-result error">❌ 工具函数模块未加载</div>';
                return;
            }
            
            let testHTML = '<h4>工具函数测试结果：</h4>';
            
            // 测试formatDateTime
            try {
                const formatted = utils.formatDateTime(new Date(), 'YYYY-MM-DD');
                testHTML += '<div class="test-result success">✅ formatDateTime: ' + formatted + '</div>';
            } catch (e) {
                testHTML += '<div class="test-result error">❌ formatDateTime: ' + e.message + '</div>';
            }
            
            // 测试truncateString
            try {
                const truncated = utils.truncateString('这是一个很长的测试字符串', 10);
                testHTML += '<div class="test-result success">✅ truncateString: ' + truncated + '</div>';
            } catch (e) {
                testHTML += '<div class="test-result error">❌ truncateString: ' + e.message + '</div>';
            }
            
            // 测试generateUniqueId
            try {
                const id = utils.generateUniqueId('test');
                testHTML += '<div class="test-result success">✅ generateUniqueId: ' + id + '</div>';
            } catch (e) {
                testHTML += '<div class="test-result error">❌ generateUniqueId: ' + e.message + '</div>';
            }
            
            results.innerHTML = testHTML;
        }
        
        function testConstants() {
            const results = document.getElementById('function-test-results');
            const constants = window.CONSTANTS;
            
            if (!constants) {
                results.innerHTML = '<div class="test-result error">❌ 常量模块未加载</div>';
                return;
            }
            
            let testHTML = '<h4>常量测试结果：</h4>';
            
            testHTML += '<div class="test-result success">✅ MAX_CONCURRENCY: ' + constants.MAX_CONCURRENCY + '</div>';
            testHTML += '<div class="test-result success">✅ APP_INFO.NAME: ' + constants.APP_INFO.NAME + '</div>';
            testHTML += '<div class="test-result success">✅ STORAGE_KEYS: ' + JSON.stringify(constants.STORAGE_KEYS) + '</div>';
            
            results.innerHTML = testHTML;
        }
        
        // 监听模块加载完成
        let checkModulesInterval = setInterval(() => {
            if (window.CONSTANTS && window.ModuleExports && window.ModuleExports['utils.js']) {
                clearInterval(checkModulesInterval);
                
                addTestResult('所有基础模块加载完成！', 'success');
                document.getElementById('loading').style.display = 'none';
                document.getElementById('test-results').style.display = 'block';
                updateModuleStatus();
            }
        }, 100);
        
        // 5秒后如果还没加载完成，显示错误
        setTimeout(() => {
            if (checkModulesInterval) {
                clearInterval(checkModulesInterval);
                addTestResult('模块加载超时，可能存在问题', 'error');
                document.getElementById('loading').style.display = 'none';
                document.getElementById('test-results').style.display = 'block';
                updateModuleStatus();
            }
        }, 5000);
        
        addTestResult('开始加载测试模块...', 'info');
    </script>
    
    <!-- 加载基础模块进行测试 -->
    <script src="src/constants.js"></script>
    <script src="src/utils.js"></script>
</body>
</html>