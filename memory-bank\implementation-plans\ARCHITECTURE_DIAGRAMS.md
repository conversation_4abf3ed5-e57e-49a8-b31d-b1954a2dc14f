# 零服务器模块化方案 - 架构图表

## 🏗️ 文件功能架构图

```mermaid
graph TB
    subgraph "入口层 Entry Layer"
        A[index.html<br/>🏠 运行时入口<br/>- DOM结构定义<br/>- CDN库加载<br/>- 模块加载检测]
        B[loader.js<br/>🔄 动态加载器<br/>- ES模块转换<br/>- 依赖图管理<br/>- 错误处理]
    end
    
    subgraph "配置层 Configuration Layer"
        C[constants.js<br/>⚙️ 全局配置<br/>- MAX_CONCURRENCY<br/>- API_ENDPOINTS<br/>- STORAGE_KEYS]
        D[config/local-config.js<br/>🔑 API密钥<br/>- Kimi API Key<br/>- 本地设置<br/>- 不入版本控制]
    end
    
    subgraph "功能模块层 Feature Modules"
        E[parser.js<br/>📝 文本解析<br/>- parseTxtContent<br/>- evaluateConversationWithKimi<br/>- 对话分组]
        F[storage.js<br/>💾 数据存储<br/>- localStorage管理<br/>- CSV导出<br/>- 数据序列化]
        G[charts.js<br/>📊 图表渲染<br/>- ECharts初始化<br/>- 实时数据更新<br/>- 4种图表类型]
        H[drag-upload.js<br/>📁 文件上传<br/>- 拖拽处理<br/>- 文件验证<br/>- 事件绑定]
    end
    
    subgraph "应用层 Application Layer"
        I[main.js<br/>🎯 应用编排<br/>- 业务流程控制<br/>- TaskPool并发<br/>- UI状态管理]
    end
    
    subgraph "外部依赖 External Dependencies"
        J[ECharts 5.x<br/>📈 图表库]
        K[PapaParse 5.x<br/>📄 CSV处理]
        L[Kimi API<br/>🤖 AI分析]
    end
    
    %% 依赖关系
    A --> B
    B --> C
    B --> E
    B --> F
    B --> G
    B --> H
    B --> I
    
    C --> E
    C --> F
    C --> G
    C --> I
    
    D --> E
    D --> I
    
    E --> I
    F --> I
    G --> I
    H --> I
    
    I --> J
    F --> K
    E --> L
    
    %% 样式定义
    classDef entryLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef configLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef featureLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef appLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef externalLayer fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class A,B entryLayer
    class C,D configLayer
    class E,F,G,H featureLayer
    class I appLayer
    class J,K,L externalLayer
```

## 📊 数据流程图

```mermaid
graph TD
    subgraph "用户交互 User Interaction"
        A[用户选择.txt文件<br/>📁 File Selection]
        B[拖拽或点击上传<br/>🖱️ Drag & Drop]
        C[开始分析按钮<br/>▶️ Start Analysis]
    end
    
    subgraph "文件处理 File Processing"
        D[drag-upload.js<br/>文件验证]
        E[main.js<br/>文件读取<br/>FileReader API]
        F[parser.js<br/>parseTxtContent<br/>文本解析]
    end
    
    subgraph "AI分析 AI Analysis"
        G[parser.js<br/>evaluateConversationWithKimi]
        H[Kimi API<br/>🤖 AI服务<br/>90s超时]
        I[AI响应解析<br/>JSON + Regex回退]
    end
    
    subgraph "并发控制 Concurrency Control"
        J[main.js<br/>TaskPool<br/>MAX_CONCURRENCY=50]
        K[队列管理<br/>Queue Management]
        L[错误重试<br/>Error Retry]
    end
    
    subgraph "数据处理 Data Processing"
        M[结果聚合<br/>Result Aggregation]
        N[数据转换<br/>Data Transformation]
        O[storage.js<br/>数据持久化<br/>localStorage]
    end
    
    subgraph "可视化 Visualization"
        P[charts.js<br/>updateChartsData]
        Q[ECharts渲染<br/>📊 4种图表类型]
        R[DOM表格更新<br/>📋 Results Table]
    end
    
    subgraph "数据导出 Export"
        S[用户点击导出<br/>📥 Export Button]
        T[storage.js<br/>exportToCSV]
        U[Papa.unparse<br/>CSV序列化]
        V[文件下载<br/>💾 Download]
    end
    
    %% 主要数据流
    A --> B
    B --> D
    D --> E
    E --> F
    
    F --> G
    G --> H
    H --> I
    I --> G
    
    C --> J
    J --> K
    F --> J
    G --> J
    K --> L
    
    I --> M
    M --> N
    N --> O
    
    M --> P
    P --> Q
    M --> R
    
    S --> T
    O --> T
    T --> U
    U --> V
    
    %% 实时更新循环
    O --> P
    
    %% 样式定义
    classDef userLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef processLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef aiLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef concurrentLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef dataLayer fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef visualLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef exportLayer fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    
    class A,B,C userLayer
    class D,E,F processLayer
    class G,H,I aiLayer
    class J,K,L concurrentLayer
    class M,N,O dataLayer
    class P,Q,R visualLayer
    class S,T,U,V exportLayer
```

## 🔄 模块加载时序图

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant HTML as 🏠 index.html
    participant Loader as 🔄 loader.js
    participant Modules as 📦 各模块
    participant Main as 🎯 main.js
    participant UI as 🖥️ 用户界面
    
    User->>HTML: 双击打开文件
    HTML->>HTML: 加载CDN库(ECharts, PapaParse)
    HTML->>Loader: 执行模块加载器
    
    Loader->>Loader: 检测ES模块支持
    
    alt 浏览器支持ES模块
        Loader->>Modules: 按依赖图顺序加载
        Note over Loader,Modules: constants → storage/parser/charts/upload → main
        
        loop 每个模块
            Loader->>Modules: fetch模块代码
            Loader->>Loader: 转换ES语法
            Loader->>Modules: 执行模块并注册
        end
        
        Loader->>Main: 启动主应用
        Main->>Main: initializeApp()
        Main->>UI: 显示主界面
        UI->>User: 系统就绪，可以使用
        
    else 浏览器不支持
        Loader->>UI: 显示升级提示
        UI->>User: 请升级浏览器
    end
```

## 📋 状态机图

```mermaid
stateDiagram-v2
    [*] --> Loading: 页面加载
    
    Loading --> ModuleCheck: 检测ES模块支持
    
    ModuleCheck --> LoadingModules: 支持ES模块
    ModuleCheck --> BrowserError: 不支持ES模块
    
    LoadingModules --> Initializing: 模块加载完成
    Initializing --> Ready: 应用初始化完成
    
    Ready --> FileSelected: 用户选择文件
    FileSelected --> Processing: 开始分析
    
    Processing --> Processing: 处理文件中
    Processing --> Completed: 所有文件处理完成
    Processing --> Error: 处理出错
    
    Completed --> Ready: 等待新操作
    Completed --> Exporting: 用户点击导出
    
    Error --> Ready: 错误恢复
    
    Exporting --> Ready: 导出完成
    
    BrowserError --> [*]: 用户关闭页面
    
    note right of Loading
        显示加载状态
        初始化CDN库
    end note
    
    note right of Processing
        TaskPool并发处理
        实时UI更新
        AI API调用
    end note
    
    note right of Ready
        所有功能可用
        等待用户操作
    end note
```

## 🏗️ 组件依赖图

```mermaid
graph LR
    subgraph "基础设施 Infrastructure"
        Constants[constants.js<br/>🔧 配置常量]
        Config[local-config.js<br/>🔑 API密钥]
    end
    
    subgraph "核心服务 Core Services"
        Storage[storage.js<br/>💾 存储服务]
        Parser[parser.js<br/>📝 解析服务]
        Charts[charts.js<br/>📊 图表服务]
        Upload[drag-upload.js<br/>📁 上传服务]
    end
    
    subgraph "应用逻辑 Application Logic"
        Main[main.js<br/>🎯 主应用]
        TaskPool[TaskPool<br/>⚙️ 并发控制]
        EventBus[事件总线<br/>📡 组件通信]
    end
    
    subgraph "外部服务 External Services"
        ECharts[ECharts<br/>📈 图表引擎]
        PapaParse[PapaParse<br/>📄 CSV引擎]
        KimiAPI[Kimi API<br/>🤖 AI引擎]
    end
    
    %% 依赖关系
    Constants --> Storage
    Constants --> Parser
    Constants --> Charts
    Constants --> Main
    
    Config --> Parser
    Config --> Main
    
    Storage --> Main
    Parser --> Main
    Charts --> Main
    Upload --> Main
    
    Main --> TaskPool
    Main --> EventBus
    
    Charts --> ECharts
    Storage --> PapaParse
    Parser --> KimiAPI
    
    %% 样式
    classDef infrastructure fill:#e3f2fd,stroke:#1976d2
    classDef services fill:#e8f5e8,stroke:#388e3c
    classDef application fill:#f3e5f5,stroke:#7b1fa2
    classDef external fill:#ffebee,stroke:#d32f2f
    
    class Constants,Config infrastructure
    class Storage,Parser,Charts,Upload services
    class Main,TaskPool,EventBus application
    class ECharts,PapaParse,KimiAPI external
```

## 📊 性能监控图

```mermaid
graph TB
    subgraph "性能指标 Performance Metrics"
        A[加载时间<br/>⏱️ Load Time]
        B[内存使用<br/>🧠 Memory Usage]
        C[并发效率<br/>⚡ Concurrency]
        D[API响应<br/>🌐 API Response]
    end
    
    subgraph "监控点 Monitoring Points"
        E[模块加载<br/>📦 Module Loading]
        F[文件解析<br/>📝 File Parsing]
        G[AI分析<br/>🤖 AI Analysis]
        H[图表渲染<br/>📊 Chart Rendering]
    end
    
    subgraph "优化策略 Optimization"
        I[懒加载<br/>🔄 Lazy Loading]
        J[内存清理<br/>🗑️ Memory Cleanup]
        K[请求合并<br/>📋 Request Batching]
        L[缓存策略<br/>💾 Caching Strategy]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    
    E --> I
    F --> J
    G --> K
    H --> L
    
    %% 数据流
    I -.-> E
    J -.-> F
    K -.-> G
    L -.-> H
```

这些图表完整展示了零服务器模块化方案的：

1. **文件功能架构** - 每个文件的职责和依赖关系
2. **数据流程** - 从用户输入到结果输出的完整数据流
3. **模块加载时序** - 动态加载的详细过程
4. **应用状态机** - 不同运行状态的转换
5. **组件依赖** - 模块间的依赖关系
6. **性能监控** - 关键性能指标和优化策略

所有图表都严格遵循您的 Copilot 指令和架构决策，确保方案的技术可行性和实施可操作性。
